"""
Service Configuration Helper

Simplified utilities to determine which services require OAuth flow
vs manual API key configuration.
"""

from typing import Dict, List, Optional
from enum import Enum

class ServiceAuthType(Enum):
    """Service authentication types"""
    OAUTH = "oauth"
    MANUAL = "manual"

class ServiceConfigHelper:
    """Helper class to manage service configuration requirements"""
    
    # Service configurations
    SERVICES = {
        # OAuth services
        "supabase": {
            "auth_type": ServiceAuthType.OAUTH,
            "display_name": "Supabase",
            "description": "Database and backend services",
            "help_url": "https://supabase.com/docs/guides/api",
            "env_vars": ["SUPABASE_URL", "SUPABASE_KEY"]
        },
        
        #  Manual setup services
        "stripe": {
            "auth_type": ServiceAuthType.MANUAL,
            "display_name": "Stripe",
            "description": "Payment processing services",
            "help_url": "https://stripe.com/docs/api",
            "env_vars": ["STRIPE_SECRET_KEY", "STRIPE_PUBLISHABLE_KEY"]
        },
        "openai": {
            "auth_type": ServiceAuthType.MANUAL,
            "display_name": "OpenAI",
            "description": "AI language models",
            "help_url": "https://platform.openai.com/docs/quickstart",
            "env_vars": ["OPENAI_API_KEY"],
            "instructions": "To use OpenAI's GPT models:\n1. Sign up at https://platform.openai.com\n2. Navigate to API Keys section\n3. Create a new API key\n4. Enter it below"
        },
        "openweathermap": {
            "auth_type": ServiceAuthType.MANUAL,
            "display_name": "OpenWeatherMap",
            "description": "Weather data services",
            "help_url": "https://openweathermap.org/api",
            "env_vars": ["OPENWEATHERMAP_API_KEY"],
            "instructions": "To use OpenWeatherMap API:\n1. Sign up at https://openweathermap.org\n2. Go to API keys section\n3. Generate a new API key\n4. Enter it below"
        },
        "firebase": {
            "auth_type": ServiceAuthType.MANUAL,
            "display_name": "Firebase",
            "description": "Google's app development platform",
            "help_url": "https://firebase.google.com/docs",
            "env_vars": ["FIREBASE_CONFIG"],
            "instructions": "To use Firebase:\n1. Create a project in Firebase Console\n2. Go to Project Settings\n3. Copy the configuration object\n4. Enter it below"
        }
    }
    
    @classmethod
    def get_service_config(cls, service_name: str) -> Optional[Dict]:
        """Get service configuration by name"""
        return cls.SERVICES.get(service_name.lower())
    
    @classmethod
    def requires_oauth(cls, service_name: str) -> bool:
        """Check if service requires OAuth flow"""
        config = cls.get_service_config(service_name)
        return config and config["auth_type"] == ServiceAuthType.OAUTH
    
    @classmethod
    def get_services_by_type(cls, auth_type: ServiceAuthType) -> List[str]:
        """Get services by authentication type"""
        return [name for name, config in cls.SERVICES.items() 
                if config["auth_type"] == auth_type]
    
    @classmethod
    def get_all_services(cls) -> List[str]:
        """Get all supported service names"""
        return list(cls.SERVICES.keys())
    
    @classmethod
    def generate_action_request(cls, service_name: str) -> Optional[Dict]:
        """Generate action request for service setup"""
        config = cls.get_service_config(service_name)
        if not config:
            return None
        
        service_name = service_name.lower()
        
        if config["auth_type"] == ServiceAuthType.OAUTH:
            return {
                "action_type": "setup_service_oauth",
                "service_name": service_name,
                "oauth_flow": True,
                "instructions": f"We'll help you connect to {config['display_name']} through a secure OAuth flow. This will automatically configure your project with the correct credentials.",
                "help_url": config["help_url"]
            }
        else:
            return {
                "action_type": "setup_service",
                "service_name": service_name,
                "environment_variable": [
                    {
                        "field_name": env_var,
                        "display_name": env_var.replace("_", " ").title()
                    }
                    for env_var in config["env_vars"]
                ],
                "instructions": config.get("instructions", f"Configure {config['display_name']} by providing the required credentials."),
                "help_url": config["help_url"]
            }


def requires_oauth(service_name: str) -> bool:
    """Check if a service requires OAuth flow"""
    return ServiceConfigHelper.requires_oauth(service_name)

def generate_service_action_request(service_name: str) -> Optional[Dict]:
    """Generate action request for service setup"""
    return ServiceConfigHelper.generate_action_request(service_name)