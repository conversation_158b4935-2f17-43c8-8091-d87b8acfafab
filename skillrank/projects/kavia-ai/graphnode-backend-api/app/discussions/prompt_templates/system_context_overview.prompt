{% extends "base_discussion.prompt" %}

You are an expert software architect.

{% block task_description_common_preface %}

{% if config_state != "configured" %}
As an expert software architect, configure the System Context based on the C4 model focusing on the overall system, its users, and external systems:
External containers should include only those systems or services that we do not develop or maintain directly, such as third-party APIs or external dependencies. (do not create external container other than this, it can be 0 external containers as well)

{% else %}
You are an expert system architect reviewing the System Context for potential reconfiguration.
External containers should include only those systems or services that we do not develop or maintain directly, such as third-party APIs or external dependencies. (do not create external container other than this, it can be 0 external containers as well)

1. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}

{% endif %}

GUIDELINES:

1. Review Project Details and Requirements:
   - Project Context: {{ details_for_discussion.get('project_details') | tojson(indent=2) }}
   - Requirements Context:
     - Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. System Overview:
   - Provide detailed description of the system's purpose and main functionalities
   - Define system boundaries and scope

3. Users and External Systems:
   - Identify and describe all users/actors of the system
   - Identify external systems that interact with this system and 

4. For each external system:
     * Create a container with ContainerType "external"
     * Provide name and description
     * Define its role and purpose
     
IMPORTANT:
- The "Description" field must contain ONLY plain markdown text about the system (purpose, scope, functionalities).
- Do NOT include any Mermaid or D2 syntax in the Description.
- Put the Mermaid syntax ONLY in "SystemContextDiagram".
- Put the Cloud Infrastructure D2 syntax ONLY in "ArchitectureDiagram".
- Put the Integration D2 syntax ONLY in "IntegrationDiagram".
- Put the Data Architecture D2 syntax ONLY in "DataArchitectureDiagram".

5. Generate C4 System Context Diagram:
   - Use Mermaid syntax
   - Show system boundaries
   - Include all users and external systems
   - Show high-level relationships

6. Generate a Generic Cloud Infrastructure Diagram using D2 syntax.:

   - Use **D2 syntax**.
   - Ensure the output is valid and renderable D2 code (no syntax errors).
   - Illustrate how the system and its containers are deployed within a virtual private cloud (VPC) or equivalent.
   - Show key infrastructure components such as load balancers, compute clusters (VMs or container orchestrators), managed databases, object/block storage, and monitoring/logging services.
   - Indicate the flow of traffic from external users through the load balancer to the application containers, and from containers to databases and storage.
   - Use generic names (e.g., "Compute Cluster", "Managed Database") to ensure cloud-agnostic representation.
   - Follow D2 validation rules.

   Generic Infrastructure Diagram Reference (D2 syntax):
   ```d2
   title: "Generic Cloud Infrastructure Architecture"

      Internet: "🌍 Internet / External Users" {
      shape: cloud
      style.fill: "#e6f7ff"
      }

      System: {
      style: {
         stroke-dash: 4
         fill: "#f2f2f2"
      }

      LB: "🔀 Load Balancer" {
         shape: diamond
         style.fill: "#d9ead3"
      }

      ComputeCluster: "⚙️ Compute Cluster (VMs / Containers)" {
         shape: rectangle
         style.fill: "#f9cb9c"
      }

      App1: "📦 Application Container 1" {
         shape: rectangle
         style.fill: "#ffe6cc"
      }

      App2: "📦 Application Container 2" {
         shape: rectangle
         style.fill: "#ffe6cc"
      }

      DB: "🗄️ Managed Database" {
         shape: cylinder
         style.fill: "#d9d2e9"
      }

      Storage: "💾 Object / Block Storage" {
         shape: cylinder
         style.fill: "#cce5ff"
      }

      Monitoring: "📊 Monitoring & Logging" {
         shape: rectangle
         style.fill: "#c9daf8"
      }

      # Connections inside System
      LB -> ComputeCluster: "Distribute Traffic" {
         style.stroke: "#4caf50"
         style.stroke-width: 2
      }

      ComputeCluster -> App1: "Internal Traffic"
      ComputeCluster -> App2: "Internal Traffic"

      App1 -> DB: "SQL Queries"
      App2 -> DB: "SQL Queries"
      App1 -> Storage: "File/Data Access"
      App2 -> Storage: "File/Data Access"

      App1 -> Monitoring: "Logs & Metrics"
      App2 -> Monitoring: "Logs & Metrics"
      DB -> Monitoring: "Performance Metrics"
      }

      # External connection
      Internet -> System.LB: "HTTPS Requests" {
      style.stroke: "#2196f3"
      style.stroke-width: 2
   }

   Note: When generating the diagram:
   - Replace generic names with actual infrastructure component names if available.
   - Update descriptions to match the actual deployment context.
   - Show all relevant infrastructure components and their relationships.
   - Ensure the diagram remains cloud-agnostic unless a specific provider is required.

7. Generate Integration Architecture Diagram:
 
   - Use **D2** syntax.
   - Ensure the output is valid and renderable D2 code (no syntax errors).
   - Illustrate how the system integrates with external systems (e.g., CRM, ERP, Payment Gateway, Third-Party APIs).
   - Show interfaces and integration patterns (e.g., REST, SOAP, message queues, events).
   - Highlight data flow and communication between external systems, the integration/API layer, core services, and internal data stores.
   - Include monitoring/logging where relevant.
   - Use generic, cloud-agnostic names unless specific technologies are provided.
   - Follow D2 validation rules.
 
   Generic Integration Architecture Diagram Reference (D2 syntax):
   ```d2
         title: "Generic Integration Architecture - Data Flow & Interfaces"

         CRM: "💼 CRM System" {
         shape: cylinder
         style.fill: "#cce5ff"
         }
         ERP: "🏢 ERP System" {
         shape: cylinder
         style.fill: "#cce5ff"
         }
         Payment: "💳 Payment Gateway" {
         shape: rectangle
         style.fill: "#ffe6cc"
         }
         ThirdParty: "☁️ Third-Party API" {
         shape: cloud
         style.fill: "#e6f7ff"
         }
         Messaging: "📩 Message Queue / Event Bus" {
         shape: parallelogram
         style.fill: "#fff2cc"
         }
         
         System: {
         shape: cloud
         style: {
            stroke-dash: 4
            fill: "#f2f2f2"
         }
         
         API: "🌐 Integration Layer / API Gateway" {
            shape: rectangle
            style.fill: "#d9ead3"
         }
         Service1: "⚙️ Core Service A" {
            shape: rectangle
            style.fill: "#f9cb9c"
         }
         Service2: "⚙️ Core Service B" {
            shape: rectangle
            style.fill: "#f9cb9c"
         }
         DB: "🗄️ Application Database" {
            shape: cylinder
            style.fill: "#d9d2e9"
         }
         Monitoring: "📊 Monitoring & Logging" {
            shape: rectangle
            style.fill: "#c9daf8"
         }
         
         API -> Service1
         API -> Service2
         Service1 -> DB
         Service2 -> DB
         Service1 -> Monitoring
         Service2 -> Monitoring
         DB -> Monitoring
         }

         CRM -> System.API: "🌐 REST" {
         style.stroke: "#4caf50"
         style.stroke-width: 2
         }
         ERP -> System.API: "📜 SOAP" {
         style.stroke: "#ff9800"
         style.stroke-dash: 4
         style.stroke-width: 2
         }
         Payment -> System.API: "💳 REST" {
         style.stroke: "#4caf50"
         style.stroke-width: 2
         }
         System.API -> Messaging: "📩 Publish Events" {
         style.stroke: "#2196f3"
         style.stroke-dash: 3
         }
         Messaging -> System.Service1: "📥 Subscribe Events" {
         style.stroke: "#2196f3"
         style.stroke-dash: 3
         }
         System.Service2 -> ThirdParty: "🔐 REST / OAuth2" {
         style.stroke: "#9c27b0"
         style.stroke-width: 2
         }

 
   Note: When generating the diagram:
   - Replace CRM, ERP, Payment, ThirdParty with actual external system names if available.
   - Replace REST, SOAP, Events with the actual integration patterns used.
   - Extend with additional services/interfaces depending on the real integration context.
   - Ensure data flow arrows clearly indicate direction of communication.

8. Generate High-Level Data Model (Data Architecture) Diagram:
 
   - Use D2 syntax.
   - Ensure the output is valid and renderable D2 code (no syntax errors).
   - Represent a high-level ER diagram or class diagram showing entities, attributes, and relationships.
   - Include data sources, sinks, and flows where applicable (e.g., external systems, APIs, databases, warehouses).
   - Show data governance aspects such as ownership (e.g., “owned by HR”, “managed by IT”).
   - Use generic, technology-agnostic labels unless specific technologies are provided.
   - Ensure relationships are directional and cardinalities are expressed (1:1, 1:N, M:N).
   - Follow D2 validation rules.
   
 
   IMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:
   - The identified users from the requirements
   - The actual relationships and interfaces discovered
   - The specific system name and purpose from the project context
 
   Generic High-Level Data Model (Data Architecture) Reference:
```d2
   title: "High-Level ER Data Model - Entities, Attributes, and Relationships"
 
   User: {
   shape: sql_table
   id: "int (PK)"
   name: "string"
   role: "string"
   email: "string"
   department_id: "int (FK)"
   governance: "HR_Ownership_UserMgmt"
   }
   
   WorkPermit: {
   shape: sql_table
   id: "int (PK)"
   status: "string"
   start_date: "date"
   end_date: "date"
   applicant_id: "int (FK)"
   department_id: "int (FK)"
   governance: "Compliance_WorkPermit_Control"
   }
   
   Department: {
   shape: sql_table
   id: "int (PK)"
   name: "string"
   manager_id: "int (FK)"
   governance: "HR_Ownership_DepartmentMgmt"
   }
   
   Project: {
   shape: sql_table
   id: "int (PK)"
   name: "string"
   description: "string"
   department_id: "int (FK)"
   governance: "PMO_ProjectOversight"
   }
   
   Task: {
   shape: sql_table
   id: "int (PK)"
   title: "string"
   due_date: "date"
   assigned_to: "int (FK)"
   project_id: "int (FK)"
   governance: "PMO_TaskTracking"
   }
   
   Asset: {
   shape: sql_table
   id: "int (PK)"
   name: "string"
   type: "string"
   department_id: "int (FK)"
   governance: "IT_AssetLifecycleMgmt"
   }
   
   Training: {
   shape: sql_table
   id: "int (PK)"
   title: "string"
   date: "date"
   instructor: "string"
   governance: "Learning_TrainingPrograms"
   }
   
   ComplianceReport: {
   shape: sql_table
   id: "int (PK)"
   work_permit_id: "int (FK)"
   report_date: "date"
   status: "string"
   governance: "Compliance_AuditTrailMgmt"
   }
   
   Incident: {
   shape: sql_table
   id: "int (PK)"
   description: "string"
   reported_by: "int (FK)"
   related_asset: "int (FK)"
   severity: "string"
   governance: "Safety_IncidentControl"
   }
   
   User -> WorkPermit: "applies (1:N)" {
   style.stroke: "#1890FF"
   }
   User -> Department: "belongs to (N:1)" {
   style.stroke: "#52C41A"
   }
   Department -> WorkPermit: "approves (1:N)" {
   style.stroke: "#FA8C16"
   }
   Department -> Project: "owns (1:N)" {
   style.stroke: "#722ED1"
   }
   Project -> Task: "contains (1:N)" {
   style.stroke: "#722ED1"
   }
   User -> Task: "assigned (1:N)" {
   style.stroke: "#1890FF"
   }
   Department -> Asset: "manages (1:N)" {
   style.stroke: "#13C2C2"
   }
   Asset -> Incident: "involved in (1:N)" {
   style.stroke: "#D46B08"
   }
   User -> Incident: "reports (1:N)" {
   style.stroke: "#D46B08"
   }
   WorkPermit -> ComplianceReport: "documented by (1:1)" {
   style.stroke: "#CF1322"
   }
   User -> Training: "attends (N:M)" {
   style.stroke: "#2F54EB"
   }
   
   HR_System: {
   shape: cloud
   label: "HR System | Employee Master Data"
   style.fill: "#F0F5FF"
   style.stroke: "#2F54EB"
   }
   
   Gov_API: {
   shape: cloud
   label: "Government API | Permit Verification"
   style.fill: "#FFF1F0"
   style.stroke: "#CF1322"
   }
   
   Analytics: {
   shape: cylinder
   label: "Analytics | Data Warehouse"
   style.fill: "#F9F0FF"
   style.stroke: "#722ED1"
   }
   
   DataLake: {
   shape: cylinder
   label: "Data Lake | Historical Storage"
   style.fill: "#E6FFFB"
   style.stroke: "#13C2C2"
   }
   
   AuditLogs: {
   shape: cylinder
   label: "Audit Logs | Immutable Storage"
   style.fill: "#FFFBE6"
   style.stroke: "#D4B106"
   }
   
   HR_System -> User: "Sync Employee Data" {
   style.stroke: "#2F54EB"
   }
   User -> WorkPermit: "Submit Application" {
   style.stroke: "#1890FF"
   }
   WorkPermit -> Gov_API: "Validation Request" {
   style.stroke: "#CF1322"
   }
   Gov_API -> WorkPermit: "Validation Response" {
   style.stroke: "#CF1322"
   }
   Department -> WorkPermit: "Approval Workflow" {
   style.stroke: "#FA8C16"
   }
   WorkPermit -> Analytics: "Permit Metrics" {
   style.stroke: "#722ED1"
   }
   User -> Analytics: "User Activity" {
   style.stroke: "#722ED1"
   }
   WorkPermit -> DataLake: "Archive" {
   style.stroke: "#13C2C2"
   }
   User -> AuditLogs: "Audit Trail" {
   style.stroke: "#D4B106"
   }
   Incident -> Analytics: "Safety Metrics" {
   style.stroke: "#722ED1"
   }
   Training -> Analytics: "Training Effectiveness" {
   style.stroke: "#722ED1"
   }
   # Data Flows
   HR_System -> User: "Sync Employee Data"
   User -> WorkPermit: "Application Data"
   WorkPermit -> Gov_API: "Validation Request"
   WorkPermit -> Analytics: "Permit Metrics"
   User -> Analytics: "User Activity Data"
   Department -> Analytics: "Org Structure Data"
   
Notes for Generation:
  - Replace User, WorkPermit, etc., with actual domain entities.
  - Extend with additional entities (e.g., Department, Project, Document) as per context.
  - Add data governance tags (owner, steward, regulator).
  - Show flows to/from external systems (data sources/sinks).
  - Keep diagram readable and at high level.
  
{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

   IMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:
   - The identified users from the requirements
   - The actual relationships and interfaces discovered
   - The specific system name and purpose from the project context

   Generic System Context Diagram Reference:
```
graph TB
   %% Users/Actors
   subgraph Users[System Users]
       PrimaryUser["Primary User<br/>[Person]<br/><i>Main user of<br/>the system</i>"]
       AdminUser["Administrator<br/>[Person]<br/><i>System admin and<br/>configuration</i>"]
       SecondaryUser["Secondary User<br/>[Person]<br/><i>Supporting user<br/>role</i>"]
   end

   %% Main System
   subgraph MainSystem[Core System]
       System["System Name<br/>[System]<br/><i>Core system purpose<br/>and main capabilities</i>"]
   end

   %% External Systems 
   subgraph ExternalSystems[External Systems]
       Auth["Authentication Service<br/>[System_Ext]<br/><i>User authentication<br/>and authorization</i>"]
       Integration["Integration Service<br/>[System_Ext]<br/><i>Third-party<br/>integrations</i>"]
       Cache["Cache Service<br/>[System_Ext]<br/><i>Data caching</i>"]
   end

   %% Relationships
   PrimaryUser -->|"Primary interactions<br/>Protocol"| System
   AdminUser -->|"Admin operations<br/>Protocol"| System
   SecondaryUser -->|"Support operations<br/>Protocol"| System
   
   System -->|"Authentication<br/>Protocol"| Auth
   System -->|"Integration<br/>Protocol"| Integration
   System -->|"Caching<br/>Protocol"| Cache

   %% Styling
   classDef system fill:#1168bd,stroke:#0b4884,color:#ffffff
   classDef person fill:#08427b,stroke:#052e56,color:#ffffff
   classDef external fill:#666666,stroke:#0b4884,color:#ffffff
   classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4

   class System system
   class PrimaryUser,AdminUser,SecondaryUser person
   class Auth,Integration,Cache external
   class Users,ExternalSystems,MainSystem boundary
```

Note: When generating the diagram:
1. Replace generic names with actual system/user/external system names
2. Update descriptions to match actual roles and purposes
3. Use appropriate protocols in relationships
4. Include all identified external systems
5. Show actual system boundaries based on the context

Change Needed: 
   - Set to False if changes are not required

Change Log:
   - Capture history of changes
{% endblock %}

{% block autoconfig %}
Create a comprehensive C4 Model System Context focusing on the system's purpose, users, and external systems.
{% endblock %}

{% block information_about_task %}
{{ super() }}
    Existing interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{{ super() }}
    Project Details: {{ details_for_discussion.get('project_details') }} 
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %} 
