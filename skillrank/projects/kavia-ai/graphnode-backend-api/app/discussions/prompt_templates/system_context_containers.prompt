{% extends "base_discussion.prompt" %}

You are an expert software architect.

{% block task_description_common_preface %}

{% if config_state != "configured" %}
As an expert software architect, design the internal container architecture based on the C4 model:
Note : Each container (node) should be treated as a separate repository, independently managed and deployed.

{% else %}
You are an expert system architect reviewing the Container architecture for potential reconfiguration.

1. Original Container Architecture:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Containers: {{ bg_info.get('containers', {}) | tojson(indent=2) }}
   - Interfaces: {{ bg_info.get('interfaces', {}) | tojson(indent=2) }}

2. Current Architecture (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated containers: {{ new_bg.get('containers', {}) | tojson(indent=2) }}
   - Updated interfaces: {{ new_bg.get('interfaces', {}) | toj<PERSON>(indent=2) }}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}

{% endif %}

GUIDELINES:  

1. Review Requirements Context:
   - Project Context: {{ details_for_discussion.get('project_details') | tojson(indent=2) }}
   - Requirements:
     - Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Container Architecture:
   - Create containers as child nodes based on:
     * System responsibilities
     * Architecture strategy
     * Functional/Non-Functional requirements
   - For new Containers:
     * Use IDs like 'NEW-ARCH-1', 'NEW-ARCH-2', etc.
   - Container Properties:
     * Title: Clear, descriptive name
     * Description: Purpose and responsibilities
     * Type: Container
     * ContainerType: "internal" 

3. Interface Definition and Creation:
   - AUTOMATICALLY CREATE interface nodes as child nodes when containers need to communicate
   - For each interface node:
     * Create as a child node with unique ID (e.g., 'NEW-INTERFACE-1', 'NEW-INTERFACE-2')
     * Set Type: "Interface"
     * Properties to include:
       - Title: Clear, descriptive name
       - Description: Detailed description of the interface purpose
       - InterfaceType: Determine appropriate type based on:
         - Communication patterns (synchronous/asynchronous)
         - Performance requirements
         - Reliability needs
         - Integration constraints
       - Protocol: Specific protocol used
       - DataFormat: Data exchange format
       - ApiSignature: Examples of API calls or message formats

   Example interface creation scenarios:
   - Container-to-container communication requires REST API
   - Asynchronous processing needs Message Queue interface
   - Database access requires Database Interface
   - File operations need File Interface

4. Define interactions between containers through these interfaces

IMPORTANT:
- The "Description" field must contain ONLY plain markdown text about the system (purpose, scope, functionalities).
- Do NOT include any Mermaid or D2 syntax in the Description.
- Put the Mermaid syntax ONLY in "SystemContextDiagram".
- Put the Cloud Infrastructure D2 syntax ONLY in "ArchitectureDiagram".
- Put the Integration D2 syntax ONLY in "IntegrationDiagram".
- Put the Data Architecture D2 syntax ONLY in "DataArchitectureDiagram".

5. Generate C4 Container Diagram:
   - Use Mermaid syntax
   - Show all containers
   - Include interfaces
   - Show relationships and dependencies

6. Generate a Generic Cloud Infrastructure Diagram using D2 syntax.:

   - Use **D2 syntax**.
   - Ensure the output is valid and renderable D2 code (no syntax errors).
   - Illustrate how the system and its containers are deployed within a virtual private cloud (VPC) or equivalent.
   - Show key infrastructure components such as load balancers, compute clusters (VMs or container orchestrators), managed databases, object/block storage, and monitoring/logging services.
   - Indicate the flow of traffic from external users through the load balancer to the application containers, and from containers to databases and storage.
   - Use generic names (e.g., "Compute Cluster", "Managed Database") to ensure cloud-agnostic representation.
   - Follow D2 validation rules.

   Generic Infrastructure Diagram Reference (D2 syntax):
   ```d2
   title: "Generic Cloud Infrastructure Architecture"

      Internet: "🌍 Internet / External Users" {
      shape: cloud
      style.fill: "#e6f7ff"
      }

      System: {
      style: {
         stroke-dash: 4
         fill: "#f2f2f2"
      }

      LB: "🔀 Load Balancer" {
         shape: diamond
         style.fill: "#d9ead3"
      }

      ComputeCluster: "⚙️ Compute Cluster (VMs / Containers)" {
         shape: rectangle
         style.fill: "#f9cb9c"
      }

      App1: "📦 Application Container 1" {
         shape: rectangle
         style.fill: "#ffe6cc"
      }

      App2: "📦 Application Container 2" {
         shape: rectangle
         style.fill: "#ffe6cc"
      }

      DB: "🗄️ Managed Database" {
         shape: cylinder
         style.fill: "#d9d2e9"
      }

      Storage: "💾 Object / Block Storage" {
         shape: cylinder
         style.fill: "#cce5ff"
      }

      Monitoring: "📊 Monitoring & Logging" {
         shape: rectangle
         style.fill: "#c9daf8"
      }

      # Connections inside System
      LB -> ComputeCluster: "Distribute Traffic" {
         style.stroke: "#4caf50"
         style.stroke-width: 2
      }

      ComputeCluster -> App1: "Internal Traffic"
      ComputeCluster -> App2: "Internal Traffic"

      App1 -> DB: "SQL Queries"
      App2 -> DB: "SQL Queries"
      App1 -> Storage: "File/Data Access"
      App2 -> Storage: "File/Data Access"

      App1 -> Monitoring: "Logs & Metrics"
      App2 -> Monitoring: "Logs & Metrics"
      DB -> Monitoring: "Performance Metrics"
      }

      # External connection
      Internet -> System.LB: "HTTPS Requests" {
      style.stroke: "#2196f3"
      style.stroke-width: 2
   }

   Note: When generating the diagram:
   - Replace generic names with actual infrastructure component names if available.
   - Update descriptions to match the actual deployment context.
   - Show all relevant infrastructure components and their relationships.
   - Ensure the diagram remains cloud-agnostic unless a specific provider is required.

7. Generate Integration Architecture Diagram:
 
   - Use **D2** syntax.
   - Ensure the output is valid and renderable D2 code (no syntax errors).
   - Illustrate how the system integrates with external systems (e.g., CRM, ERP, Payment Gateway, Third-Party APIs).
   - Show interfaces and integration patterns (e.g., REST, SOAP, message queues, events).
   - Highlight data flow and communication between external systems, the integration/API layer, core services, and internal data stores.
   - Include monitoring/logging where relevant.
   - Use generic, cloud-agnostic names unless specific technologies are provided.
   - Follow D2 validation rules.
 
   Generic Integration Architecture Diagram Reference (D2 syntax):
   ```d2
         title: "Generic Integration Architecture - Data Flow & Interfaces"

         CRM: "💼 CRM System" {
         shape: cylinder
         style.fill: "#cce5ff"
         }
         ERP: "🏢 ERP System" {
         shape: cylinder
         style.fill: "#cce5ff"
         }
         Payment: "💳 Payment Gateway" {
         shape: rectangle
         style.fill: "#ffe6cc"
         }
         ThirdParty: "☁️ Third-Party API" {
         shape: cloud
         style.fill: "#e6f7ff"
         }
         Messaging: "📩 Message Queue / Event Bus" {
         shape: parallelogram
         style.fill: "#fff2cc"
         }
         
         System: {
         shape: cloud
         style: {
            stroke-dash: 4
            fill: "#f2f2f2"
         }
         
         API: "🌐 Integration Layer / API Gateway" {
            shape: rectangle
            style.fill: "#d9ead3"
         }
         Service1: "⚙️ Core Service A" {
            shape: rectangle
            style.fill: "#f9cb9c"
         }
         Service2: "⚙️ Core Service B" {
            shape: rectangle
            style.fill: "#f9cb9c"
         }
         DB: "🗄️ Application Database" {
            shape: cylinder
            style.fill: "#d9d2e9"
         }
         Monitoring: "📊 Monitoring & Logging" {
            shape: rectangle
            style.fill: "#c9daf8"
         }
         
         API -> Service1
         API -> Service2
         Service1 -> DB
         Service2 -> DB
         Service1 -> Monitoring
         Service2 -> Monitoring
         DB -> Monitoring
         }

         CRM -> System.API: "🌐 REST" {
         style.stroke: "#4caf50"
         style.stroke-width: 2
         }
         ERP -> System.API: "📜 SOAP" {
         style.stroke: "#ff9800"
         style.stroke-dash: 4
         style.stroke-width: 2
         }
         Payment -> System.API: "💳 REST" {
         style.stroke: "#4caf50"
         style.stroke-width: 2
         }
         System.API -> Messaging: "📩 Publish Events" {
         style.stroke: "#2196f3"
         style.stroke-dash: 3
         }
         Messaging -> System.Service1: "📥 Subscribe Events" {
         style.stroke: "#2196f3"
         style.stroke-dash: 3
         }
         System.Service2 -> ThirdParty: "🔐 REST / OAuth2" {
         style.stroke: "#9c27b0"
         style.stroke-width: 2
         }

 
   Note: When generating the diagram:
   - Replace CRM, ERP, Payment, ThirdParty with actual external system names if available.
   - Replace REST, SOAP, Events with the actual integration patterns used.
   - Extend with additional services/interfaces depending on the real integration context.
   - Ensure data flow arrows clearly indicate direction of communication.

8. Generate High-Level Data Model (Data Architecture) Diagram:
 
   - Use D2 syntax.
   - Ensure the output is valid and renderable D2 code (no syntax errors).
   - Represent a high-level ER diagram or class diagram showing entities, attributes, and relationships.
   - Include data sources, sinks, and flows where applicable (e.g., external systems, APIs, databases, warehouses).
   - Show data governance aspects such as ownership (e.g., “owned by HR”, “managed by IT”).
   - Use generic, technology-agnostic labels unless specific technologies are provided.
   - Ensure relationships are directional and cardinalities are expressed (1:1, 1:N, M:N).
   - Follow D2 validation rules.
   
 
   IMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:
   - The identified users from the requirements
   - The actual relationships and interfaces discovered
   - The specific system name and purpose from the project context
 
   Generic High-Level Data Model (Data Architecture) Reference:
```d2
   title: "High-Level ER Data Model - Entities, Attributes, and Relationships"
 
   User: {
   shape: sql_table
   id: "int (PK)"
   name: "string"
   role: "string"
   email: "string"
   department_id: "int (FK)"
   governance: "HR_Ownership_UserMgmt"
   }
   
   WorkPermit: {
   shape: sql_table
   id: "int (PK)"
   status: "string"
   start_date: "date"
   end_date: "date"
   applicant_id: "int (FK)"
   department_id: "int (FK)"
   governance: "Compliance_WorkPermit_Control"
   }
   
   Department: {
   shape: sql_table
   id: "int (PK)"
   name: "string"
   manager_id: "int (FK)"
   governance: "HR_Ownership_DepartmentMgmt"
   }
   
   Project: {
   shape: sql_table
   id: "int (PK)"
   name: "string"
   description: "string"
   department_id: "int (FK)"
   governance: "PMO_ProjectOversight"
   }
   
   Task: {
   shape: sql_table
   id: "int (PK)"
   title: "string"
   due_date: "date"
   assigned_to: "int (FK)"
   project_id: "int (FK)"
   governance: "PMO_TaskTracking"
   }
   
   Asset: {
   shape: sql_table
   id: "int (PK)"
   name: "string"
   type: "string"
   department_id: "int (FK)"
   governance: "IT_AssetLifecycleMgmt"
   }
   
   Training: {
   shape: sql_table
   id: "int (PK)"
   title: "string"
   date: "date"
   instructor: "string"
   governance: "Learning_TrainingPrograms"
   }
   
   ComplianceReport: {
   shape: sql_table
   id: "int (PK)"
   work_permit_id: "int (FK)"
   report_date: "date"
   status: "string"
   governance: "Compliance_AuditTrailMgmt"
   }
   
   Incident: {
   shape: sql_table
   id: "int (PK)"
   description: "string"
   reported_by: "int (FK)"
   related_asset: "int (FK)"
   severity: "string"
   governance: "Safety_IncidentControl"
   }
   
   User -> WorkPermit: "applies (1:N)" {
   style.stroke: "#1890FF"
   }
   User -> Department: "belongs to (N:1)" {
   style.stroke: "#52C41A"
   }
   Department -> WorkPermit: "approves (1:N)" {
   style.stroke: "#FA8C16"
   }
   Department -> Project: "owns (1:N)" {
   style.stroke: "#722ED1"
   }
   Project -> Task: "contains (1:N)" {
   style.stroke: "#722ED1"
   }
   User -> Task: "assigned (1:N)" {
   style.stroke: "#1890FF"
   }
   Department -> Asset: "manages (1:N)" {
   style.stroke: "#13C2C2"
   }
   Asset -> Incident: "involved in (1:N)" {
   style.stroke: "#D46B08"
   }
   User -> Incident: "reports (1:N)" {
   style.stroke: "#D46B08"
   }
   WorkPermit -> ComplianceReport: "documented by (1:1)" {
   style.stroke: "#CF1322"
   }
   User -> Training: "attends (N:M)" {
   style.stroke: "#2F54EB"
   }
   
   HR_System: {
   shape: cloud
   label: "HR System | Employee Master Data"
   style.fill: "#F0F5FF"
   style.stroke: "#2F54EB"
   }
   
   Gov_API: {
   shape: cloud
   label: "Government API | Permit Verification"
   style.fill: "#FFF1F0"
   style.stroke: "#CF1322"
   }
   
   Analytics: {
   shape: cylinder
   label: "Analytics | Data Warehouse"
   style.fill: "#F9F0FF"
   style.stroke: "#722ED1"
   }
   
   DataLake: {
   shape: cylinder
   label: "Data Lake | Historical Storage"
   style.fill: "#E6FFFB"
   style.stroke: "#13C2C2"
   }
   
   AuditLogs: {
   shape: cylinder
   label: "Audit Logs | Immutable Storage"
   style.fill: "#FFFBE6"
   style.stroke: "#D4B106"
   }
   
   HR_System -> User: "Sync Employee Data" {
   style.stroke: "#2F54EB"
   }
   User -> WorkPermit: "Submit Application" {
   style.stroke: "#1890FF"
   }
   WorkPermit -> Gov_API: "Validation Request" {
   style.stroke: "#CF1322"
   }
   Gov_API -> WorkPermit: "Validation Response" {
   style.stroke: "#CF1322"
   }
   Department -> WorkPermit: "Approval Workflow" {
   style.stroke: "#FA8C16"
   }
   WorkPermit -> Analytics: "Permit Metrics" {
   style.stroke: "#722ED1"
   }
   User -> Analytics: "User Activity" {
   style.stroke: "#722ED1"
   }
   WorkPermit -> DataLake: "Archive" {
   style.stroke: "#13C2C2"
   }
   User -> AuditLogs: "Audit Trail" {
   style.stroke: "#D4B106"
   }
   Incident -> Analytics: "Safety Metrics" {
   style.stroke: "#722ED1"
   }
   Training -> Analytics: "Training Effectiveness" {
   style.stroke: "#722ED1"
   }

   # Data Flows
   HR_System -> User: "Sync Employee Data"
   User -> WorkPermit: "Application Data"
   WorkPermit -> Gov_API: "Validation Request"
   WorkPermit -> Analytics: "Permit Metrics"
   User -> Analytics: "User Activity Data"
   Department -> Analytics: "Org Structure Data"

   
Notes for Generation:
  - Replace User, WorkPermit, etc., with actual domain entities.
  - Extend with additional entities (e.g., Department, Project, Document) as per context.
  - Add data governance tags (owner, steward, regulator).
  - Show flows to/from external systems (data sources/sinks).
  - Keep diagram readable and at high level.

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

   IMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:
   - The containers identified in the system context
   - The actual interfaces and relationships discovered
   - The specific technologies and protocols used
   - The external systems including databases from the context

   Generic Container Diagram Reference:
```
graph TB
   %% External Users
   PrimaryUser["Primary User<br/>[Person]<br/><i>Main system user</i>"]
   AdminUser["Admin User<br/>[Person]<br/><i>System administrator</i>"]

   subgraph CoreSystem[Core System Containers]
       %% Frontend Applications
       WebUI["Web Interface<br/>[Container: Frontend Tech]<br/><i>Main user interface</i>"]
       AdminUI["Admin Interface<br/>[Container: Frontend Tech]<br/><i>Administration interface</i>"]
       
       %% API Layer
       ApiGateway["API Gateway<br/>[Container: API Tech]<br/><i>Request routing and<br/>authentication</i>"]
       
       %% Core Services
       MainService["Main Service<br/>[Container: Backend Tech]<br/><i>Core business logic</i>"]
       
       SupportService["Support Service<br/>[Container: Backend Tech]<br/><i>Supporting functionality</i>"]
       
       %% Message Handling
       MessageBus["Message Bus<br/>[Container: Message Tech]<br/><i>Async communication</i>"]
   end

   %% External Systems
   PrimaryDB["Main Database<br/>[System_Ext]<br/><i>Primary data storage</i>"]
   CacheSystem["Cache Service<br/>[System_Ext]<br/><i>Data caching</i>"]
   AuthService["Auth System<br/>[System_Ext]<br/><i>Authentication</i>"]

   %% Relationships
   PrimaryUser -->|"Uses<br/>Protocol"| WebUI
   AdminUser -->|"Uses<br/>Protocol"| AdminUI
   
   WebUI -->|"API calls<br/>Protocol"| ApiGateway
   AdminUI -->|"API calls<br/>Protocol"| ApiGateway
   
   ApiGateway -->|"Routes<br/>Protocol"| MainService
   ApiGateway -->|"Routes<br/>Protocol"| SupportService
   
   MainService -->|"Reads/Writes<br/>Protocol"| PrimaryDB
   MainService -->|"Caches<br/>Protocol"| CacheSystem
   
   MainService -->|"Publishes<br/>Protocol"| MessageBus
   SupportService -->|"Subscribes<br/>Protocol"| MessageBus
   
   ApiGateway -->|"Authenticates<br/>Protocol"| AuthService

   %% Styling
   classDef person fill:#08427b,stroke:#052e56,color:#ffffff
   classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
   classDef external fill:#666666,stroke:#0b4884,color:#ffffff
   classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4

   class PrimaryUser,AdminUser person
   class WebUI,AdminUI,ApiGateway,MainService,SupportService,MessageBus container
   class PrimaryDB,CacheSystem,AuthService external
   class CoreSystem boundary
```

Note: When generating the diagram:
1. Replace generic names with actual container and system names
2. Use actual technologies in container descriptions
3. Show correct protocols in relationships
4. Include all identified containers and external systems
5. Represent actual system boundaries and dependencies

Change Needed: 
   - Set to False if changes are not required

Change Log:
   - Capture history of changes
{% endblock %}

{% block autoconfig %}
Design the internal container architecture focusing on containers and their interfaces.
{% endblock %}

{% block information_about_task %}
{{ super() }}
    Existing Containers:
    {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Existing interfaces: {{ details_for_discussion.get('interfaces') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{{ super() }}
    Project Details: {{ details_for_discussion.get('project_details') }} 
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Existing Containers: {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Other Containers in the system: {{ details_for_discussion.get('other_containers') | tojson(indent=2) }}
{% endblock %} 
