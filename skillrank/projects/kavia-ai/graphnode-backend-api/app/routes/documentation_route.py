# app/routes/documentation.py
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, status, Depends, Body
from fastapi.responses import StreamingResponse, FileResponse
from typing import Optional, List, Tuple

import urllib
from app.utils.file_utils.upload_utils import upload_and_process, s3_client, get_tenant_bucket
from app.utils.validation_utils import validate_name
from app.connection.tenant_middleware import get_tenant_id
from io import BytesIO
import json
from datetime import datetime
from botocore.exceptions import ClientError
from app.connection.establish_db_connection import NodeDB, get_node_db
from app.core.constants import MODERN_PDF_CSS
from app.models.documentation_model import CreateSection, SectionReorder, SectionOrder, CreateDocumentationRequest
from app.classes.S3Handler import S3<PERSON><PERSON><PERSON>
from typing import Optional
import traceback


import base64
import io
import os
import re
import subprocess
import uuid
from urllib.parse import quote

from weasyprint import HTML, CSS
import mistune
import requests
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.shared import Inches
from docx import Document
from PIL import Image
import time
import asyncio

router = APIRouter(
    prefix="/documentation",
    tags=["documentation"],
    responses={404: {"description": "Not found"}}
)

DOC_TYPES = {
    "PRD": "product_requirements",
    "SAD": "system_architecture",
    "API": "api_documentation",
    "SAVED": "saved_documentation",
}

class DocumentationService:
    def __init__(self, tenant_id: str):
        self.s3_base_path = "attachments"
        self.tenant_id = tenant_id
        self.bucket = get_tenant_bucket(tenant_id)

    def list_doc_types(self) -> List[str]:
        """List all available documentation types"""
        return list(DOC_TYPES.keys())

    async def list_versions(
        self, 
        project_id: int, 
        doc_type: str,
        interface_id: Optional[int] = None
    ) -> List[dict]:
        """List all versions of documents for a specific doc type"""
        if doc_type not in DOC_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid document type. Allowed types: {list(DOC_TYPES.keys())}"
            )

        try:

       
      
            prefix = f"{self.s3_base_path}/project-id-{project_id}/{DOC_TYPES[doc_type]}/"
            
            response = s3_client.list_objects_v2(
                Bucket=self.bucket,
                Prefix=prefix,
                Delimiter='/'
            )

            if 'CommonPrefixes' in response:
                if(doc_type == 'SAVED'):
                    saved_docs = []
                    for prefix in response['CommonPrefixes']:
                        prefix = prefix['Prefix']
                        contents = s3_client.list_objects_v2(
                            Bucket = self.bucket,
                            Prefix = prefix
                        )

                        files = []
                        if 'Contents' in contents:
                                for item in contents['Contents']:
                                    file_name = item['Key'].split('/')[-1]
                                    if file_name:
                                        files.append({
                                            "file_name": file_name,
                                            "size": item['Size'],
                                            "last_modified": item['LastModified'].isoformat(),
                                            "key": item['Key']
                                        })

                        saved_docs.extend(files)
                    return [{"version": 1,
                             "files": saved_docs
                            }]
                else:
                    versions = []
                    for prefix in response['CommonPrefixes']:
                        version_prefix = prefix['Prefix']
                        version = version_prefix.split('/')[-2]
                        if version.startswith('v'):
                            version_contents = s3_client.list_objects_v2(
                                Bucket=self.bucket,
                                Prefix=version_prefix
                            )
                            
                            files = []
                            if 'Contents' in version_contents:
                                for item in version_contents['Contents']:
                                    file_name = item['Key'].split('/')[-1]
                                    if file_name:
                                        files.append({
                                            "file_name": file_name,
                                            "size": item['Size'],
                                            "last_modified": item['LastModified'].isoformat(),
                                            "key": item['Key']
                                        })

                            versions.append({
                                "version": version[1:],
                                "files": files
                            })

                    return sorted(versions, key=lambda x: x['version'], reverse=True)

        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error accessing S3: {str(e)}"
            )

    async def download_document(
        self, 
        project_id: int, 
        doc_type: str, 
        version: str,
        folder_path: str, 
        file_name: str,
        interface_id: Optional[int] = None
    ):
        """Download a specific document version"""
        if doc_type not in DOC_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid document type. Allowed types: {list(DOC_TYPES.keys())}"
            )

        try:
  
            s3_key = f"{self.s3_base_path}/project-id-{project_id}/{DOC_TYPES[doc_type]}/{folder_path}/{file_name}"
            
            response = s3_client.get_object(
                Bucket=self.bucket,
                Key=s3_key
            )

            return StreamingResponse(
                response['Body'].iter_chunks(),
                media_type=response['ContentType'],
                headers={
                    'Content-Disposition': f'attachment; filename="{sanitize_filename(file_name)}"',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            )

        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Document not found"
                )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error downloading from S3: {str(e)}"
            )


@router.get("/types")
async def list_doc_types(tenant_id: str = Depends(get_tenant_id)):
    """List all available documentation types"""
    service = DocumentationService(tenant_id)
    return service.list_doc_types()

@router.get("/versions/{project_id}/{doc_type}")
async def list_versions(
    project_id: int,
    doc_type: str,
    interface_id: Optional[int] = None,
    tenant_id: str = Depends(get_tenant_id)
):
    """List all versions for a specific doc type"""
    try:
        service = DocumentationService(tenant_id)
        return await service.list_versions(project_id, doc_type, interface_id)
    except Exception as e:
        print(f"Error in list_versions: {str(e)}")
        print("Traceback:")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/download/{project_id}/{doc_type}/{version}/{folder_path}/{file_name}")
async def download_document(
    project_id: int,
    doc_type: str,
    version: str,
    folder_path: str,
    file_name: str,
    interface_id: Optional[int] = None,
    tenant_id: str = Depends(get_tenant_id)
):
    """Download a specific document version"""
    try:
        service = DocumentationService(tenant_id)
        return await service.download_document(
            project_id=project_id,
            doc_type=doc_type,
            version=version,
            file_name=file_name,
            folder_path=folder_path,
            interface_id=interface_id
        )
    except Exception as e:
        print(f"Error in download_document: {str(e)}")
        print("Traceback:")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/doc/{project_id}/{doc_type}")
async def get_doc_versions(
    project_id: int,
    doc_type: str,
    interface_id: Optional[int] = None,
    tenant_id: str = Depends(get_tenant_id)
):
    """Get all versions of a specific document type"""
    try:
        await validate_api_documentation(doc_type, interface_id)
        service = DocumentationService(tenant_id)
        return await service.list_versions(
            project_id=project_id,
            doc_type=doc_type,
            interface_id=interface_id
        )
    except Exception as e:
        print(f"Error in get_doc_versions: {str(e)}")
        print("Traceback:")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/latest/{project_id}/{doc_type}")
async def get_latest_version(
    project_id: int,
    doc_type: str,
    interface_id: Optional[int] = None,
    tenant_id: str = Depends(get_tenant_id)
):
    """Get the latest version of a specific document type"""
    try:
        await validate_api_documentation(doc_type, interface_id)
        service = DocumentationService(tenant_id)
        versions = await service.list_versions(
            project_id=project_id,
            doc_type=doc_type,
            interface_id=interface_id
        )
        
        if not versions:
            raise HTTPException(status_code=404, detail="No versions found")
            
        return versions[0]  # Already sorted by version in list_versions
    except Exception as e:
        print(f"Error in get_latest_version: {str(e)}")
        print("Traceback:")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

async def validate_api_documentation(documentation_type: str, interface_id: Optional[int]):
    """Validate interface_id requirement for API documentation"""
    if documentation_type == "API" and not interface_id:
        raise HTTPException(
            status_code=400,
            detail="interface_id is required for API documentation"
        )

async def get_documentation_filename(
    project_id: int, 
    documentation_type: str, 
    node_db: NodeDB,
    interface_id: Optional[int] = None
) -> str:
    """Generate filename for documentation PDF"""
    try:
        await validate_api_documentation(documentation_type, interface_id)
        
        if documentation_type == "API":
            default_file_name = f"API_interface_{interface_id}_{project_id}.pdf"
        else:
            default_file_name = f"{documentation_type.upper()}_{project_id}.pdf"
        
        result = await node_db.get_documentation(
            project_id=project_id, 
            documentation_type=documentation_type,
            interface_id=interface_id
        )
        
        if result and len(result) > 0:
            title = result[0]['doc'].get("Title", "").strip()
            if title:
                current_time = datetime.now().replace(microsecond=0).isoformat()
                return f"{title}_{current_time}.pdf"
                
        return default_file_name
        
    except Exception:
        if documentation_type == "API":
            return f"API_interface_{interface_id}_{project_id}.pdf"
        return f"{documentation_type.upper()}_{project_id}.pdf"

async def generate_documentation_pdf(
    project_id: int, 
    documentation_type: str, 
    node_db,
    interface_id: Optional[int] = None
) -> tuple:
    """Generate PDF content and return both the PDF bytes and the document result"""
    try:
        await validate_api_documentation(documentation_type, interface_id)
        
        result = await node_db.get_documentation(
            project_id=project_id, 
            documentation_type=documentation_type,
            interface_id=interface_id
        )
        
        if not result or len(result) == 0:
            raise HTTPException(status_code=404, detail="Documentation not found")

        doc = result[0]['doc']
        sections = result[0]['sections']

        # create mistune parser and pass to builder
        markdown_parser = mistune.create_markdown(plugins=['table', 'footnotes', 'strikethrough'])
        builder = HTMLDocumentBuilder(markdown_parser=markdown_parser)

        def sync_build_html():
            for section in sections:
                builder.process_section(section['Title'], section.get('Content', ''))
            return builder.build_full_html(doc)

        # Offload blocking build to thread
        full_html = await asyncio.to_thread(sync_build_html)

        # Offload WeasyPrint PDF generation to thread
        def sync_render_pdf(html_str):
            css = CSS(string=MODERN_PDF_CSS)
            html = HTML(string=html_str)
            return html.write_pdf(stylesheets=[css], presentational_hints=True, optimize_size=('fonts','images'))

        pdf_bytes = await asyncio.to_thread(sync_render_pdf, full_html)

        return pdf_bytes, result

    except HTTPException:
        raise
    except Exception as e:
        print(f"PDF Generation Error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/create_documentation")
async def create_documentation(
    request: CreateDocumentationRequest,
    node_db: NodeDB = Depends(get_node_db)
):
    """Create documentation root and sections for a project or API interface"""
    try:
        # Validate documentation type and interface_id combination
        await validate_api_documentation(request.documentation_type, request.interface_id)
        
        # Create documentation root node
        doc_root = await node_db.create_documentation_root(
            project_id=request.project_id,
            documentation_type=request.documentation_type,
            interface_id=request.interface_id
        )
        
        if not doc_root:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to create {request.documentation_type} documentation root"
            )

        # Get the created documentation
        result = await node_db.get_documentation(
            project_id=request.project_id,
            documentation_type=request.documentation_type,
            interface_id=request.interface_id
        )
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail="Documentation not found after creation"
            )
            
        return result[0] if result else []

    except HTTPException:
        raise
    except Exception as e:
        print(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Error creating documentation: {str(e)}"
        )
             
@router.get("/")
async def get_documentation(
    project_id: int,
    documentation_type: str = "SAD",
    interface_id: Optional[int] = None,
    node_db: NodeDB = Depends(get_node_db)
):
    """Get documentation for a project or API interface"""
    try:
        await validate_api_documentation(documentation_type, interface_id)
        result = await node_db.get_documentation(
            project_id=project_id, 
            documentation_type=documentation_type,
            interface_id=interface_id
        )
        return result or []
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/create_section")
async def create_section(
    section: CreateSection,
    documentation_type: str = "SAD",
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        await validate_api_documentation(documentation_type, section.interface_id)
        
        # Validate section name
        is_valid, error_message = validate_name(section.section_name, type="Section")
        if not is_valid:
            raise HTTPException(status_code=400, detail=error_message)
            
        # Check for duplicate section names
        existing_docs = await node_db.get_documentation(
            section.project_id, 
            documentation_type,
            interface_id=section.interface_id
        )
        
        if existing_docs and len(existing_docs) > 0:
            sections = existing_docs[0].get('sections', [])
            if any(s['Title'].lower() == section.section_name.strip().lower() for s in sections):
                raise HTTPException(status_code=400, detail="Section name already exists")

        created_section = await node_db.create_section(
            project_id=section.project_id,
            section_name=section.section_name.strip(),
            documentation_type=documentation_type,
            interface_id=section.interface_id
        )
        return created_section
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in create_section: {str(e)}")
        print("Traceback:")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))
    
@router.put("/reorder-sections")
async def reorder_sections(
    reorder_data: SectionReorder,
    documentation_type: str = "SAD",
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        await validate_api_documentation(documentation_type, reorder_data.interface_id)
        
        reorder_data_dict = json.loads(reorder_data.model_dump_json())
        result = await node_db.reorder_sections(
            project_id=reorder_data_dict.get("project_id"),
            section_orders=reorder_data_dict.get("section_orders"),
            documentation_type=documentation_type,
            interface_id=reorder_data_dict.get("interface_id")
        )
        return result
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/sections/{project_id}/{section_id}")
async def delete_section(
    project_id: int,
    section_id: int,
    documentation_type: str = "SAD",
    interface_id: Optional[int] = None,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        await validate_api_documentation(documentation_type, interface_id)
        result = await node_db.delete_section(
            project_id=project_id,
            section_id=section_id,
            documentation_type=documentation_type,
            interface_id=interface_id
        )
        return {"message": "Section deleted successfully", "deleted_section": result}
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/download-pdf/{project_id}")
async def download_documentation_pdf(
    project_id: int,
    documentation_type: str = "SAD",
    interface_id: Optional[int] = None,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        await validate_api_documentation(documentation_type, interface_id)
        
        pdf, _ = await generate_documentation_pdf(
            project_id=project_id, 
            documentation_type=documentation_type,
            node_db=node_db,
            interface_id=interface_id
        )
        
        file_name = await get_documentation_filename(
            project_id=project_id,
            documentation_type=documentation_type,
            node_db=node_db,
            interface_id=interface_id
        )

        return StreamingResponse(
            BytesIO(pdf),
            media_type="application/pdf",
            headers={
                'Content-Disposition': f'attachment; filename="{sanitize_filename(file_name)}"',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        )
    except Exception as e:
        print(f"PDF Generation Error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sync-to-s3/{project_id}")
async def sync_documentation_to_s3(
    project_id: int,
    documentation_type: str = "SAD",
    interface_id: Optional[int] = None,
    tenant_id: str = Depends(get_tenant_id),
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        await validate_api_documentation(documentation_type, interface_id)
        
        pdf, _ = await generate_documentation_pdf(
            project_id=project_id,
            documentation_type=documentation_type,
            node_db=node_db,
            interface_id=interface_id
        )
        
        file_name = await get_documentation_filename(
            project_id=project_id,
            documentation_type=documentation_type,
            node_db=node_db,
            interface_id=interface_id
        )
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        from tempfile import SpooledTemporaryFile
        spool = SpooledTemporaryFile()
        spool.write(pdf)
        spool.seek(0)
        
        upload_file = UploadFile(
            filename=file_name,
            file=spool,
            headers={"content-type": "application/pdf"}
        )

        try:
            result = await upload_document(
                project_id=project_id,
                doc_type=documentation_type.upper(),
                version=timestamp,
                file=upload_file,
                tenant_id=tenant_id,
                interface_id=interface_id
            )
            return result
        finally:
            spool.close()

    except Exception as e:
        print(f"Sync to S3 Error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    
@router.post("/fetch-saved-docs")
async def fetch_saved_docs(
    request_data: dict = Body(...),
    tenant_id: str = Depends(get_tenant_id)
):
    try:
        project_id = request_data.get('project_id')
        doc_type = request_data.get('doc_type')
        version = request_data.get('version', '')
        folder_id = request_data.get('folder_id', '')

        s3_handler = S3Handler(tenant_id)
        
        if(doc_type == "SAVED"):
            path = f"attachments/project-id-{project_id}/{DOC_TYPES[doc_type]}/{folder_id}"
        else:
            path = f"attachments/project-id-{project_id}/{DOC_TYPES[doc_type]}/v{version}"

        files = s3_handler.list_all_filenames(path)
        if(len(files) > 0):
            return {
                "message": "Files fetched successfully",
                "files": files
            }
        else: 
            return {
                "message": "No files found",
                "files": []
            }
    except Exception as e:
        raise HTTPException(status_code=404, detail="An error occured.")


    
@router.post("/upload")
async def upload_document(
    project_id: int = Form(...),
    doc_type: str = Form(...),
    version: str = Form(...),
    file: UploadFile = File(...),
    folder_id: str = Form(...),
    tenant_id: str = Depends(get_tenant_id),
    interface_id: Optional[int] = Form(None),
    description: Optional[str] = Form(None)
):
    """Upload a new document"""
    try:
        await validate_api_documentation(doc_type, interface_id)
        
        service = DocumentationService(tenant_id)
        if doc_type not in DOC_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid document type. Allowed types: {list(DOC_TYPES.keys())}"
            )

        # Modify s3_path to include interface_id for API docs

        if(doc_type != "SAVED"):
            s3_path = f"project-id-{project_id}/{DOC_TYPES[doc_type]}/v{version}"
        else:
            s3_path = f"project-id-{project_id}/{DOC_TYPES[doc_type]}/{folder_id}"

        file_content = await file.read()
        result = upload_and_process(
            identifier=s3_path,
            file_content=file_content,
            file_name=file.filename,
            content_type=file.content_type,
            tenant_id=tenant_id,
            folder_name=service.s3_base_path
        )

        return {
            "message": "Document uploaded successfully",
            "location": result["s3_location"],
            "file_name": file.filename,
            "version": version,
            "bucket": result["bucket_name"],
            "interface_id": interface_id
        }
    except Exception as e:
        print(f"Upload Error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


def generate_pdf_html_content(doc, sections, markdown_parser):
    """Helper function to generate HTML content for PDF"""
    toc_items = ['<h2>Table of Contents</h2>']
    content_sections = []

    if doc.get("Description"):
        toc_items.append(
            '<li class="toc-item level-1">'
            '<a href="#introduction">1. Introduction</a>'
            '</li>'
        )

    section_start = 2 if doc.get("Description") else 1
    for idx, section in enumerate(sections, start=section_start):
        section_id = f"section-{idx}"
        section_title = section['Title']
        
        toc_items.append(
            f'<li class="toc-item level-1">'
            f'<a href="#{section_id}">{idx}. {section_title}</a>'
            f'</li>'
        )
        
        content = markdown_parser(section['Content'])
        content_sections.append(
            f'<section class="chapter" id="{section_id}">'
            f'<h1 class="chapter-title">{section_title}</h1>'
            f'<div class="chapter-content">{content}</div>'
            '</section>'
        )

    # Build HTML content
    html_content = [
        '<!DOCTYPE html>',
        '<html>',
        '<head>',
        f'<meta charset="UTF-8">',
        f'<title>{doc["Title"]}</title>',
        '</head>',
        '<body>',
        
        f'<div class="title-page">',
        f'<h1>{doc["Title"]}</h1>',
        f'<p class="metadata">Version: {doc.get("Version", "1.0")}</p>',
        f'<p class="metadata">Generated on: {datetime.now().strftime("%B %d, %Y")}</p>',
        '</div>',
        
        '<nav class="toc-nav">',
        '<ul class="toc-list">',
        ''.join(toc_items),
        '</ul>',
        '</nav>'
    ]

    # Add introduction if exists
    if doc.get("Description"):
        html_content.extend([
            '<div class="chapter">',
            '<h1 class="chapter-title" id="introduction">Introduction</h1>',
            f'<div class="chapter-content">{markdown_parser(doc["Description"])}</div>',
            '</div>'
        ])

    # Add content sections
    html_content.extend([
        ''.join(content_sections),
        '</body>',
        '</html>'
    ])

    return '\n'.join(html_content)

def sanitize_filename(filename):
    """Sanitize filename to ensure it's compatible with HTTP headers"""
    # Replace problematic Unicode characters
    unicode_replacements = {
        '\u2013': '-',    # en dash
        '\u2014': '--',   # em dash
        '\u201c': '"',    # left double quotation mark
        '\u201d': '"',    # right double quotation mark
        '\u2018': "'",    # left single quotation mark
        '\u2019': "'",    # right single quotation mark
        '\u00a0': ' ',    # non-breaking space
        '\u2026': '...',  # ellipsis
        '\u00ae': 'R',    # registered trademark
        '\u2122': 'TM',   # trademark
        '\u00a9': 'C',    # copyright
        # Add more as needed
    }
    
    for char, replacement in unicode_replacements.items():
        filename = filename.replace(char, replacement)
    
    # Further sanitize to ASCII-only characters
    import re
    filename = re.sub(r'[^\x00-\x7F]+', '_', filename)
    
    # Replace problematic characters with underscores
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # URL encode the filename to ensure it's safe in headers
    return urllib.parse.quote(filename)

class HTMLDocumentBuilder:
    """
    Builds HTML content for the documentation and inlines diagrams (Mermaid via mermaid.ink
    and D2 via d2 CLI) as base64 data URIs ready for WeasyPrint.
    """
    
    
    def __init__(self, markdown_parser=None):
        # Use passed markdown_parser or create a basic one
        self.md = markdown_parser or mistune.create_markdown(plugins=['table', 'footnotes', 'strikethrough'])
        self.toc_items: List[str] = []
        self.sections_html: List[str] = []
        self.section_index = 0

    # ---------- Mermaid helpers (adapted) ----------
    def _extract_mermaid_code_from_block(self, block: str) -> str:
        # Handle <mermaid> tags
        mermaid_match = re.search(r'<mermaid>(.*?)</mermaid>', block, re.DOTALL | re.IGNORECASE)
        if mermaid_match:
            return mermaid_match.group(1)
        # Handle ```mermaid code blocks
        mermaid_match = re.search(r'```mermaid\s*(.*?)\s*```', block, re.DOTALL | re.IGNORECASE)
        if mermaid_match:
            return mermaid_match.group(1)
        # if block itself is raw mermaid text, return it
        return block

    def _handle_mermaid_diagram(self, block: str) -> str:
        """Return HTML snippet (title + embedded image) for the mermaid diagram."""
        mermaid_code = self._extract_mermaid_code_from_block(block)
        if not mermaid_code:
            return ''
        return self.add_mermaid_diagram(mermaid_code.strip())

    def add_mermaid_diagram(self, mermaid_code: str, title: str = "Diagram", width_px: int = 800) -> str:
        """
        Render mermaid via mermaid.ink API and embed as data URI image HTML.
        Returns HTML snippet.
        """
        # try fetching generated image bytes from mermaid.ink with retries
        for attempt in range(3):
            try:
                graph_bytes = mermaid_code.encode("utf-8")
                base64_bytes = base64.urlsafe_b64encode(graph_bytes)
                base64_string = base64_bytes.decode("ascii")
                api_url = f"https://mermaid.ink/img/{base64_string}"
                timeout = 5 if attempt == 0 else 8
                response = requests.get(api_url, timeout=timeout)
                response.raise_for_status()
                image_bytes = response.content

                # validate image
                image_stream = io.BytesIO(image_bytes)
                with Image.open(image_stream) as img:
                    img.verify()

                # create data URI
                mime = Image.open(io.BytesIO(image_bytes)).get_format_mimetype()
                b64 = base64.b64encode(image_bytes).decode('ascii')
                data_uri = f"data:{mime};base64,{b64}"

                html = (
                    f'<div class="diagram">'
                    f'<h3>{title}</h3>'
                        f'<div style="text-align:center; width: 100%; height: 100%;">'
                            f'<img src="{data_uri}" style="float: left; margin-right: 15px; width: 100%; height: auto;"  alt="Mermaid diagram" />'
                        f'</div>'
                    f'</div>'
                )
                return html

            except Exception as e:
                # retry a couple times; on final fail fall back to showing code
                if attempt < 2:
                    time.sleep(0.4)
                    continue
                fallback_html = (
                    f'<div class="diagram">'
                    f'<h3>{title} (Mermaid Render Failed)</h3>'
                    f'<pre class="code">{self._escape_html(mermaid_code)}</pre>'
                    f'</div>'
                )
                return fallback_html

    # ---------- D2 helpers (adapted) ----------
    def _extract_d2_code_from_block(self, block: str) -> str:
        d2_match = re.search(r'<d2>(.*?)</d2>', block, re.DOTALL | re.IGNORECASE)
        if d2_match:
            return d2_match.group(1)
        d2_match = re.search(r'```d2\s*(.*?)\s*```', block, re.DOTALL | re.IGNORECASE)
        if d2_match:
            return d2_match.group(1)
        return block

    def _handle_d2_diagram(self, block: str) -> str:
        d2_code = self._extract_d2_code_from_block(block)
        if not d2_code:
            return ''
        return self.add_d2_diagram(d2_code.strip())

    def add_d2_diagram(self, d2_code: str, title: str = "Diagram", width_px: int = 500) -> str:
        """
        Render D2 using the d2 CLI to a PNG file, encode as data URI, and return HTML snippet.
        Falls back to code block if rendering fails.
        """
        import tempfile

        # Create temp source and output files
        with tempfile.NamedTemporaryFile(suffix=".d2", delete=False, mode="w", encoding="utf-8") as tf:
            tf.write(d2_code)
            temp_d2_file = tf.name
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as of:
            output_file = of.name

        try:
            cmd = ["d2", temp_d2_file, output_file, "--theme", "0", "--scale", "2", "--pad", "20"]
            subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=30)

            if not os.path.exists(output_file):
                raise RuntimeError("D2 did not produce output image")

            with open(output_file, "rb") as f:
                image_bytes = f.read()

            # Validate and detect MIME
            with Image.open(io.BytesIO(image_bytes)) as img:
                img.verify()
                fmt = img.format
            mime = Image.MIME.get(fmt, "image/png")

            b64 = base64.b64encode(image_bytes).decode("ascii")
            data_uri = f"data:{mime};base64,{b64}"

            html = (
                f'<div class="diagram">'
                f'<h3>{title}</h3>'
                    f'<div style="text-align:center; width: 100%; height: 100%;">'
                        f'<img src="{data_uri}" style="max-width: 75%; min-height: 65%;" />'
                    f'</div>'
                f'</div>'
            )
            return html

        except FileNotFoundError:
            # d2 CLI not installed
            return (
                f'<div class="diagram">'
                f'<h3>{title} (D2 CLI not found)</h3>'
                f'<pre class="code">{self._escape_html(d2_code)}</pre>'
                f'</div>'
            )
        except subprocess.CalledProcessError as e:
            return (
                f'<div class="diagram">'
                f'<h3>{title} (D2 render failed)</h3>'
                f'<pre class="code">{self._escape_html(d2_code)}</pre>'
                f'<p><em>CLI stderr:</em> {self._escape_html(e.stderr or str(e))}</p>'
                f'</div>'
            )
        except Exception as e:
            return (
                f'<div class="diagram">'
                f'<h3>{title} (D2 error)</h3>'
                f'<pre class="code">{self._escape_html(d2_code)}</pre>'
                f'<p><em>Error:</em> {self._escape_html(str(e))}</p>'
                f'</div>'
            )
        finally:
            for path in (temp_d2_file, output_file):
                try:
                    if os.path.exists(path):
                        os.remove(path)
                except Exception:
                    pass

    # ---------- Content block processing ----------
    def _split_content_into_blocks(self, content: str) -> List[str]:
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        content = re.sub(r'\n\s*\n', '\n\n', content.strip())
        blocks = []
        current_block = []
        lines = content.split('\n')
        in_mermaid = False
        in_d2 = False
        for line in lines:
            stripped = line.strip()
            if stripped.startswith('```mermaid') or stripped == '<mermaid>':
                if current_block:
                    blocks.append('\n'.join(current_block))
                    current_block = []
                current_block = [line]; in_mermaid = True; continue
            if (stripped == '```' and in_mermaid) or (stripped.lower() == '</mermaid>' and in_mermaid):
                current_block.append(line); blocks.append('\n'.join(current_block)); current_block = []; in_mermaid = False; continue
            if stripped.startswith('```d2') or stripped == '<d2>':
                if current_block:
                    blocks.append('\n'.join(current_block)); current_block = []
                current_block = [line]; in_d2 = True; continue
            if (stripped == '```' and in_d2) or (stripped.lower() == '</d2>' and in_d2):
                current_block.append(line); blocks.append('\n'.join(current_block)); current_block = []; in_d2 = False; continue
            if in_mermaid or in_d2:
                current_block.append(line); continue
            if stripped == '' and current_block:
                blocks.append('\n'.join(current_block)); current_block = []; continue
            elif stripped == '':
                continue
            current_block.append(line)
        if current_block:
            blocks.append('\n'.join(current_block))
        return [b.strip() for b in blocks if b.strip()]

    def _identify_block_type(self, block: str) -> Tuple[str, str]:
        block = block.strip()
        if block.startswith('<mermaid>') or block.startswith('```mermaid') or '```mermaid' in block:
            return "mermaid", block
        if block.startswith('<d2>') or block.startswith('```d2') or '```d2' in block:
            return "d2", block
        return "markdown", block

    def _handle_markdown_block(self, block: str) -> str:
        """
        Convert a non-diagram block (markdown/paragraph/table/list) into HTML using mistune.
        """
        return self.md(block)

    # ---------- Public: process content and build sections ----------
    def process_section(self, title: str, content: str):
        """
        Convert a single section to HTML (heading + content), populating TOC and sections_html.
        """
        self.section_index += 1
        section_id = f"section-{self.section_index}"
        self.toc_items.append((self.section_index, title, section_id))

        # Build section HTML by processing blocks
        blocks = self._split_content_into_blocks(content)
        html_parts: List[str] = []
        for block in blocks:
            if not block.strip():
                continue
            btype, bcontent = self._identify_block_type(block)
            if btype == "mermaid":
                html_parts.append(self._handle_mermaid_diagram(bcontent))
            elif btype == "d2":
                html_parts.append(self._handle_d2_diagram(bcontent))
            else:
                html_parts.append(self._handle_markdown_block(bcontent))

        sec_html = (
            f'<section class="chapter" id="{section_id}">'
            f'<h1 class="chapter-title">{self._escape_html(title)}</h1>'
            f'<div class="chapter-content">{"".join(html_parts)}</div>'
            f'</section>'
        )
        self.sections_html.append(sec_html)

    def build_full_html(self, doc_meta: dict) -> str:
        """
        Compose final HTML string with title page, TOC, and all sections.
        """
        title = self._escape_html(doc_meta.get("Title", "Document"))
        version = self._escape_html(doc_meta.get("Version", "1.0"))
        description = doc_meta.get("Description", "")
        # Title page
        html = [
            '<!DOCTYPE html>',
            '<html>',
            '<head>',
            '<meta charset="utf-8">',
            f'<title>{title}</title>',
            f'<style>{MODERN_PDF_CSS}</style>',
            '</head>',
            '<body>',
            '<div class="title-page">',
            f'<h1>{title}</h1>',
            f'<p class="metadata">Version: {version}</p>',
            f'<p class="metadata">Generated on: {datetime.now().strftime("%B %d, %Y")}</p>',
            '</div>'
        ]

        # TOC
        if self.toc_items:
            html.append('<nav class="toc-nav"><h2>Table of Contents</h2><ul class="toc-list">')
            for idx, title_text, sid in self.toc_items:
                html.append(f'<li class="toc-item level-1"><a href="#{sid}">{idx}. {self._escape_html(title_text)}</a></li>')
            html.append('</ul></nav>')

        # Optional description/introduction
        if description:
            html.append('<div class="chapter">')
            html.append('<h1 class="chapter-title" id="introduction">Introduction</h1>')
            html.append(f'<div class="chapter-content">{self.md(description)}</div>')
            html.append('</div>')

        # Sections
        html.extend(self.sections_html)

        html.append('</body></html>')
        return '\n'.join(html)

    @staticmethod
    def _escape_html(text: str) -> str:
        return (text.replace('&', '&amp;').replace('<', '&lt;')
                    .replace('>', '&gt;').replace('"', '&quot;'))
