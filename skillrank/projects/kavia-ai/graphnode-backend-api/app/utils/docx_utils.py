"""
DOCX utilities for converting various data structures to Word documents.
"""

import base64
import io
import json
import os
import re
import subprocess
import uuid
from io import BytesIO
from typing import Any, Dict, List, Union
from urllib.parse import quote

import docx
import requests
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.shared import Inches
from PIL import Image
import time


class DocxGenerator:
    """
    A utility class for generating DOCX documents from various data structures.
    """

    IGNORED_KEYS = {"created_by", "created_at", "configuration_state", "is_active"}

    def __init__(self):
        """Initialize the DocxGenerator."""
        self.document = None

    def create_new_document(self) -> docx.Document:
        """Create and return a new document instance."""
        self.document = docx.Document()
        return self.document

    def add_styled_heading(self, text: str, level: int = 1) -> None:
        """Add a styled heading to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        heading = self.document.add_heading(text, level=level)
        heading.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT

    def format_key_label(self, key: str) -> str:
        """
        Convert camelCase, PascalCase, or snake_case into human-readable format.
        E.g., "ArchitecturePattern" → "Architecture Pattern"
        """
        key = key.replace("_", " ")  # snake_case to space
        key = re.sub(r"(?<!^)(?=[A-Z])", " ", key)  # split camelCase or PascalCase
        return key.strip().title()

    def json_to_docx(self, data: Union[Dict, List, Any], level: int = 0) -> None:
        """
        Recursively convert JSON data to DOCX format.

        Args:
            data: The data to convert (dict, list, or primitive)
            level: Current nesting level for headings
        """
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if isinstance(data, dict):
            self._process_dict(data, level)
        elif isinstance(data, list):
            self._process_list(data, level)
        else:
            self.document.add_paragraph(str(data), style="BodyText")

    def _process_dict(self, data: Dict, level: int) -> None:
        """Process dictionary data."""
        for key, value in data.items():
            if key in self.IGNORED_KEYS:
                continue

            display_key = self.format_key_label(key)

            if isinstance(value, (dict, list)):
                self.add_styled_heading(display_key, level=level + 1)
                self.json_to_docx(value, level + 1)
            else:
                self._add_key_value_paragraph(display_key, value)

    def _process_list(self, data: List, level: int) -> None:
        """Process list data."""
        for item in data:
            if isinstance(item, (dict, list)):
                self.json_to_docx(item, level + 1)
            else:
                self.document.add_paragraph(str(item), style="List Bullet")

    def _add_key_value_paragraph(self, key: str, value: Any) -> None:
        """Add a key-value pair as a paragraph."""
        paragraph = self.document.add_paragraph()
        run = paragraph.add_run(f"{key}: ")
        run.bold = True

        if isinstance(value, str) and "\n" in value:
            for line in value.split("\n"):
                if line.strip():
                    self.document.add_paragraph(line.strip(), style="List Bullet")
        else:
            paragraph.add_run(str(value))

    def add_field(
        self,
        label: str,
        value: Any,
        default: str = "N/A",
        style: str = "BodyText",
        bullet: bool = False,
    ) -> None:
        """
        Add a labeled field to the document.

        Args:
            label: The field label
            value: The field value
            default: Default value if value is None
            style: Paragraph style to use
            bullet: Whether to format list values as bullet points
        """
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if value is None:
            value = default

        if bullet and isinstance(value, list):
            for item in value:
                self.document.add_paragraph(f"• {item}", style="List Bullet")
        else:
            para = self.document.add_paragraph()
            run = para.add_run(f"{label}: ")
            run.bold = True
            para.add_run(str(value))

    def add_requirement_root_to_docx(
        self, requirement_data: Dict, add_page_break: bool = False
    ) -> None:
        """Add requirement root data to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if add_page_break:
            self.document.add_page_break()
        self.add_styled_heading("Requirement Test Case", level=1)

        self.add_field("Project Name", requirement_data.get("ProjectName"))
        self.add_field("Requirement Root", requirement_data.get("RequirementRootName"))

        # Epic section
        self.add_styled_heading("Epic", level=2)
        self.add_field("ID", requirement_data.get("EpicId"))
        self.add_field("Title", requirement_data.get("Epic"))
        self.add_field("Priority", requirement_data.get("EpicPriority"))
        self.add_field("Description", requirement_data.get("EpicDescription"))

        # User Story section
        self.add_styled_heading("User Story", level=2)
        self.add_field("ID", requirement_data.get("UserStoryId"))
        self.add_field("Title", requirement_data.get("UserStory"))
        self.add_field("Priority", requirement_data.get("UserStoryPriority"))
        self.add_field("Description", requirement_data.get("UserStoryDescription"))
        self.add_field(
            "Acceptance Criteria", requirement_data.get("UserStoryAcceptanceCriteria")
        )
        self.add_field("Story Points", requirement_data.get("UserStoryStoryPoints"))

        # Test Case section
        self.add_styled_heading("Test Case", level=2)
        self.add_field("Name", requirement_data.get("TestName"))
        self.add_field("Priority", requirement_data.get("TestPriority"))
        self.add_field("Category", requirement_data.get("TestCategory"))
        self.add_field("Type", requirement_data.get("TestType"))
        self.add_field("Level", requirement_data.get("TestLevel"))
        self.add_field("Description", requirement_data.get("TestDescription"))
        self.add_field("Pre-Conditions", requirement_data.get("PreConditions"))
        self.add_field("Expected Results", requirement_data.get("ExpectedResults"))
        self.add_field(
            "Acceptance Criteria", requirement_data.get("AcceptanceCriteria")
        )
        self.add_field(
            "Automated", "Yes" if requirement_data.get("TestAutomated") else "No"
        )

        self.add_field("Steps", requirement_data.get("Steps"), bullet=True)
        self.add_field("Tags", requirement_data.get("Tags"), bullet=True)

    def add_architectural_requirement_to_docx(
        self, data: Dict, add_page_break: bool = False
    ) -> None:
        """Add architectural requirement data to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if add_page_break:
            self.document.add_page_break()
        self.add_styled_heading(data.get("Title", "Architectural Requirement"), level=1)

        self.add_field("Type", data.get("Type"))
        self.add_field("Change Reason", data.get("change_reason"))
        self.add_field("Configuration State", data.get("configuration_state"))

        # Description section
        self.add_styled_heading("Description", level=2)
        self.document.add_paragraph(data.get("Description", ""), style="BodyText")

        # Functional Requirements
        self.add_styled_heading("Functional Requirements", level=2)
        functional_req = data.get("functional_requirements", "")
        for line in functional_req.split("\n"):
            if line.strip():
                self.document.add_paragraph(line.strip(), style="List Number")

        # Architectural Requirements
        self.add_styled_heading("Architectural Requirements", level=2)
        arch_req = data.get("architectural_requirements", "")
        for line in arch_req.split("\n"):
            if line.strip():
                self.document.add_paragraph(line.strip(), style="List Number")

        # User Inputs (parsed JSON string)
        self.add_styled_heading("User Inputs", level=2)
        self._add_user_inputs_section(data.get("user_inputs", "{}"))

        # Change Log (parsed JSON)
        self.add_styled_heading("Change Log", level=2)
        self._add_change_log_section(data.get("change_log", "[]"))

        # Details
        self.add_styled_heading("Details", level=2)
        self.document.add_paragraph(data.get("Details", "N/A"))

    def _add_user_inputs_section(self, user_inputs_json: str) -> None:
        """Add user inputs section from JSON string."""
        try:
            ui_data = json.loads(user_inputs_json)
            for k, v in ui_data.items():
                label = k.replace("_", " ").title()
                label = label.replace("Analyzed", "Analyzed").replace(
                    "Created", "Created"
                )
                self.document.add_paragraph(f"{label}: {v}", style="List Bullet")
        except (json.JSONDecodeError, Exception):
            self.document.add_paragraph("User input data not available or invalid.")

    def _add_change_log_section(self, change_log_json: str) -> None:
        """Add change log section from JSON string."""
        try:
            log_data = json.loads(change_log_json)
            for entry in log_data:
                desc = entry.get("change", "No description")
                ts = entry.get("timestamp", "Unknown time")
                self.document.add_paragraph(f"- {desc} ({ts})", style="List Bullet")
        except (json.JSONDecodeError, Exception):
            self.document.add_paragraph("Change log not available or invalid.")

    def save_to_stream(self, title: str = "document") -> io.BytesIO:
        """
        Save the document to an in-memory stream and return it.

        Args:
            title: Title for the document filename

        Returns:
            BytesIO stream containing the saved document
        """
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        file_stream = io.BytesIO()
        self.document.save(file_stream)
        file_stream.seek(0)
        return file_stream

    def generate_content_disposition_header(self, title: str) -> str:
        """
        Generate a proper Content-Disposition header for file download.

        Args:
            title: The base title for the filename

        Returns:
            Content-Disposition header string
        """
        # Sanitize and encode the filename for the Content-Disposition header
        # Fallback for older browsers (ASCII only)
        ascii_filename = re.sub(r"[^a-zA-Z0-9._-]", "_", title)
        ascii_filename = f"{ascii_filename}_details.docx"

        # Modern approach using RFC 6266 (UTF-8)
        utf8_filename = quote(f"{title}_details.docx")

        return f"attachment; filename=\"{ascii_filename}\"; filename*=UTF-8''{utf8_filename}"

    def generate_project_document(self, node_data: Dict) -> None:
        """Generate a document for a project node."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        title = node_data.get("properties", {}).get("Title", "Node Details")
        self.add_styled_heading(title, level=0)

        # Recursively add all properties to the document
        self.json_to_docx(node_data.get("properties", {}))

    def generate_requirements_document(self, requirements: List[Dict]) -> None:
        """Generate a document for multiple requirements."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        for i, test_case in enumerate(requirements):
            # Add page break for all requirements except the first one
            add_page_break = i > 0
            self.add_requirement_root_to_docx(test_case, add_page_break=add_page_break)

    def add_system_context_to_docx(self, data: Dict) -> None:
        """Add system context node data to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        self.add_styled_heading(data.get("Title", "System Context"), level=1)
        self.add_field("Type", data.get("Type"))
        self.add_field("Change Reason", data.get("change_reason"))
        self.add_field("Configuration State", data.get("overview_config_state"))
        self.add_field("Users", data.get("Users"))
        self.add_field("External Systems", data.get("ExternalSystems"))

        self.add_styled_heading("Description", level=2)
        self.document.add_paragraph(data.get("Description", ""), style="BodyText")

        self.add_styled_heading("Details", level=2)
        self.document.add_paragraph(data.get("Details", ""), style="BodyText")

        self.add_styled_heading("User Inputs", level=2)
        self._add_user_inputs_section(data.get("user_inputs", "{}"))

        self.add_styled_heading("Change Log", level=2)
        self._add_change_log_section(data.get("change_log", "[]"))

        # Add Mermaid diagram as code if exists
        diagram = data.get("SystemContextDiagram", "")
        if diagram:
            self.add_mermaid_diagram(diagram, title="System Context Diagram")
        diagram = data.get("ArchitectureDiagram", "")
        if diagram:
            self.add_d2_diagram(diagram, title="Cloud Infrastructure Diagram")
        diagram = data.get("IntegrationDiagram", "")
        if diagram:
            self.add_d2_diagram(diagram, title="Integration Architecture Diagram")
        diagram = data.get("DataArchitectureDiagram", "")
        if diagram:
            self.add_d2_diagram(diagram, title="Data Architecture Diagram")

    def add_container_to_docx(self, data: Dict, add_page_break: bool = False) -> None:
        """Add container node data to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if add_page_break:
            self.document.add_page_break()

        self.add_styled_heading(data.get("Title", "Container"), level=1)

        self.add_field("Type", data.get("Type"))
        self.add_field("Container Type", data.get("ContainerType"))
        self.add_field("Description", data.get("Description"))
        self.add_field("Platform", data.get("Selected_Platform"))
        self.add_field("Technology Stack", data.get("Selected_Tech_Stack"))
        self.add_field("Container Category", data.get("ContainerCategory"))
        self.add_field("Repository Name", data.get("Repository_Name"))
        self.add_field(
            "Implemented Requirements", data.get("ImplementedRequirementIDs")
        )
        self.add_field("User Interactions", data.get("UserInteractions"))
        self.add_field(
            "External System Interactions", data.get("ExternalSystemInteractions")
        )
        self.add_field("Has Database", "Yes" if data.get("HasDatabase") else "No")
        self.add_field("Configuration State", data.get("configuration_state"))
        self.add_field("Change Reason", data.get("change_reason"))

        self.add_styled_heading("User Inputs", level=2)
        self._add_user_inputs_section(data.get("user_inputs", "{}"))

        self.add_styled_heading("Change Log", level=2)
        self._add_change_log_section(data.get("change_log", "[]"))

        # Add container diagram as raw Mermaid syntax
        diagram = data.get("ContainerDiagram", "")
        if diagram:
            self.add_styled_heading("Container Diagram (Mermaid)", level=2)
            self.document.add_paragraph(
                "Mermaid diagram syntax (not rendered):", style="BodyText"
            )
            self.document.add_paragraph(diagram, style="BodyText")

    def add_component_to_docx(self, data: Dict, add_page_break: bool = False) -> None:
        """Add component details to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if add_page_break:
            self.document.add_page_break()

        self.add_styled_heading(data.get("Title", "Component"), level=2)

        self.add_field("ID", data.get("ID"))
        self.add_field("Type", data.get("Type"))
        self.add_field("Description", data.get("Description"))
        self.add_field("Change Reason", data.get("change_reason"))
        self.add_field("Configuration State", data.get("design_detail_state"))

        # Optional Sections (Markdown-ish)
        if data.get("Design_Details"):
            self.add_styled_heading("Design Details", level=3)
            for para in data["Design_Details"].split("\n\n"):
                self.document.add_paragraph(para.strip(), style="BodyText")

        if data.get("Functionality"):
            self.add_styled_heading("Functionality", level=3)
            for para in data["Functionality"].split("\n\n"):
                self.document.add_paragraph(para.strip(), style="BodyText")

        # User Inputs
        self.add_styled_heading("User Inputs", level=3)
        self._add_user_inputs_section(data.get("user_inputs", "{}"))

        # Change Log
        self.add_styled_heading("Change Log", level=3)
        try:
            raw_log = data.get("change_log", "{}")
            parsed = json.loads(raw_log)
            for entry in parsed.get("changes", []):
                self.document.add_paragraph(
                    f"- {entry.get('description')} ({entry.get('date')})",
                    style="List Bullet",
                )
        except Exception:
            self.document.add_paragraph(
                "Invalid or missing change log.", style="BodyText"
            )

    def add_design_node_to_docx(self, data: Dict) -> None:
        """Add design node with associated diagrams and algorithms to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        props = data.get("properties", {})
        self.add_styled_heading(props.get("Title", "Design"), level=2)

        self.add_field("Type", props.get("Type"))
        self.add_field("Description", props.get("Description"))
        self.add_field("Behavior Description", props.get("BehaviorDescription"))
        self.add_field(
            "Component Interactions", props.get("ComponentInteractionsDescription")
        )
        self.add_field("Change Needed", str(props.get("changes_needed")))
        self.add_field("Behavior Config State", props.get("behavior_config_state"))
        self.add_field(
            "Component Interactions State",
            props.get("component_interactions_config_state"),
        )
        self.add_field("Class Diagram State", props.get("class_diagrams_config_state"))

        self.add_styled_heading("Class Diagram Description", level=3)
        self.document.add_paragraph(
            props.get("ClassDiagramDescription", ""), style="BodyText"
        )

        # ClassDiagram block
        for cd in data.get("ClassDiagram", []):
            self.add_styled_heading(cd.get("Title", "Class Diagram"), level=3)
            diagram = cd.get("Diagram", "")
            if diagram:
                self.add_mermaid_diagram(diagram, title="System Context Diagram")

        # Sequence diagrams
        for sd in data.get("Sequence", []):
            self.add_styled_heading(sd.get("Title", "Sequence Diagram"), level=3)
            self.document.add_paragraph(sd.get("Description", ""), style="BodyText")
            diagram = cd.get("Diagram", "")
            if diagram:
                self.add_mermaid_diagram(diagram, title="System Context Diagram")

        # State diagrams
        for sd in data.get("StateDiagram", []):
            self.add_styled_heading(sd.get("Title", "State Diagram"), level=3)
            diagram = cd.get("Diagram", "")
            if diagram:
                self.add_mermaid_diagram(diagram, title="System Context Diagram")

        # Algorithms
        for algo in data.get("Algorithm", []):
            self.add_styled_heading(algo.get("Title", "Algorithm"), level=3)
            self.document.add_paragraph(algo.get("Details", ""), style="BodyText")

    def add_interface_to_docx(self, data: Dict, add_page_break: bool = True) -> None:
        """Add an interface node to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if add_page_break:
            self.document.add_page_break()

        self.add_styled_heading(data.get("Title", "Interface"), level=1)
        self.add_field("Type", data.get("Type"))
        self.add_styled_heading("Description", level=2)
        self.document.add_paragraph(data.get("Description", ""), style="BodyText")

    def add_documentation_to_docx(
        self, doc: Dict, sections: List[Dict], doc_type: str = "SAD"
    ) -> None:
        """Generic handler for SAD, PRD, and other documentation exports."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        self.document.add_page_break()
        title = doc.get("Title", f"{doc_type} Documentation")
        self.add_styled_heading(title, level=1)

        self.add_field("Type", doc.get("DocumentationType", doc_type))
        self.add_field("Version", doc.get("Version", "N/A"))
        self.add_styled_heading("Description", level=2)
        self.document.add_paragraph(doc.get("Description", ""), style="BodyText")

        if doc.get("Content"):
            # Check if the section contains Mermaid code and replace it with images
            content = sections.get("Content", "")
            mermaid_code_blocks = self.extract_mermaid_code(content)

            for mermaid_code in mermaid_code_blocks:
                self.add_mermaid_diagram(mermaid_code)

            self.add_styled_heading("Full Content", level=2)
            # self.document.add_paragraph(doc["Content"], style="BodyText")
            # Remove the Mermaid code from the content before adding it to the document
            content_without_mermaid = self.remove_mermaid_code(content)
            self.document.add_paragraph(content_without_mermaid, style="BodyText")

        for section in sorted(sections, key=lambda x: x.get("Order", 0)):
            self.document.add_page_break()
            self.add_styled_heading(section.get("Title", "Section"), level=2)
            self.add_field("Section Type", section.get("SectionType"))
            self.add_field("Version", section.get("Version"))
            self.add_field("Description", section.get("Description"))

            if section.get("Content"):
                # Check if the section contains Mermaid code and replace it with images
                content = section.get("Content", "")
                mermaid_code_blocks = self.extract_mermaid_code(content)

                for mermaid_code in mermaid_code_blocks:
                    self.add_mermaid_diagram(mermaid_code)

                self.add_styled_heading("Section Content", level=3)
                # Remove the Mermaid code from the content before adding it to the document
                content_without_mermaid = self.remove_mermaid_code(content)
                self.document.add_paragraph(content_without_mermaid, style="BodyText")

    def add_test_cases_section(self, test_cases: List[Dict]) -> None:
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        # 🛑 Only add a page break if the document already has content
        if self.document.paragraphs:
            self.document.add_page_break()

        self.add_styled_heading("Test Cases", level=1)

        for test in test_cases:
            props = test.get("properties", {})

            self.add_styled_heading(props.get("Title", "Unnamed Test Case"), level=2)
            self.add_field("Type", props.get("Type"))
            self.add_field("Test Level", props.get("TestLevel"))
            self.add_field("Category", props.get("Category"))
            self.add_field("Environment", props.get("TestEnvironment"))
            self.add_field("Can Be Automated", str(props.get("CanBeAutomated", False)))
            self.add_field("Description", props.get("Description"))

            if props.get("TestProcedure"):
                self.add_styled_heading("Test Procedure", level=3)
                self.document.add_paragraph(props["TestProcedure"], style="BodyText")

            if props.get("ExpectedResults"):
                self.add_styled_heading("Expected Results", level=3)
                self.document.add_paragraph(props["ExpectedResults"], style="BodyText")

            if props.get("MeasurementMetrics"):
                self.add_styled_heading("Measurement Metrics", level=3)
                for metric in props["MeasurementMetrics"]:
                    self.document.add_paragraph(f"• {metric}", style="List Bullet")

    def add_mermaid_diagram(self, mermaid_code: str, title: str = "Diagram", width: float = 6.0) -> None:
        """
        Improved version of your existing method with better timeout handling.
        """
        if not self.document:
            raise ValueError("Document not initialized.")

        self.add_styled_heading(title, level=2)

        # Try multiple times with shorter timeouts
        for attempt in range(3):
            try:
                # Generate base64 encoded URL for mermaid.ink API
                graph_bytes = mermaid_code.encode("utf-8")
                base64_bytes = base64.urlsafe_b64encode(graph_bytes)
                base64_string = base64_bytes.decode("ascii")
                
                # Construct API URL
                api_url = f"https://mermaid.ink/img/{base64_string}"
                
                # Shorter timeout, more attempts
                timeout = 5 if attempt == 0 else 8  # First attempt: quick, second: longer
                
                response = requests.get(api_url, timeout=timeout)
                response.raise_for_status()
                
                # Validate that we received a valid image
                image_stream = io.BytesIO(response.content)
                with Image.open(image_stream) as img:
                    img.verify()
                
                # Reset stream position for document insertion
                image_stream.seek(0)
                
                # Add image to document
                self.document.add_picture(image_stream, width=Inches(width))
                return  # Success! Exit function
                
            except requests.exceptions.Timeout:
                if attempt < 2:  # Not the last attempt
                    print(f"Timeout on attempt {attempt + 1}, retrying...")
                    time.sleep(0.5)  # Brief pause before retry
                    continue
                else:
                    self._add_diagram_fallback(mermaid_code, "All attempts timed out")
                    return
                    
            except Exception as e:
                if attempt < 2:  # Not the last attempt  
                    continue
                else:
                    self._add_diagram_fallback(mermaid_code, f"Failed to render: {e}")
                    return

    def _add_diagram_fallback(self, mermaid_code: str, error_message: str) -> None:
        """Enhanced fallback with better formatting."""
        # Add error message
        error_para = self.document.add_paragraph()
        error_run = error_para.add_run(f"Diagram Error: {error_message}")
        error_run.italic = True
        error_run.font.size = docx.shared.Pt(10)
        
        # Add diagram title
        title_para = self.document.add_paragraph()
        title_run = title_para.add_run("Mermaid Diagram Code:")
        title_run.bold = True
        
        # Add code block with better styling
        code_paragraph = self.document.add_paragraph()
        code_paragraph.paragraph_format.left_indent = docx.shared.Inches(0.5)
        code_paragraph.paragraph_format.space_before = docx.shared.Pt(6)
        code_paragraph.paragraph_format.space_after = docx.shared.Pt(6)
        
        code_run = code_paragraph.add_run(mermaid_code)
        code_run.font.name = "Courier New"
        code_run.font.size = docx.shared.Pt(9)


    def add_d2_diagram(self, d2_code: str, title: str = "Diagram") -> None:
        if not self.document:
            raise ValueError("Document not initialized.")

        self.add_styled_heading(title, level=2)

        temp_d2_file = f"temp_{uuid.uuid4().hex[:8]}.d2"

        output_file = f"diagram_{uuid.uuid4().hex[:8]}.png"

        try:
            # Save the D2 code to a temporary file

            with open(temp_d2_file, "w", encoding="utf-8") as f:
                f.write(d2_code)

            # Run D2 CLI with additional options for better quality

            cmd = ["d2", temp_d2_file, output_file]

            # Add optional parameters for better quality

            cmd.extend(["--theme", "0"])

            cmd.extend(["--scale", "2"])

            # Add padding for better appearance

            cmd.extend(["--pad", "20"])

            # Run D2 CLI to generate PNG

            result = subprocess.run(cmd, check=True, capture_output=True, text=True)

            # Verify file was created

            if not os.path.exists(output_file):
                raise Exception(f"D2 failed to create output file: {output_file}")

            # Add to document

            with open(output_file, "rb") as f:
                image_stream = BytesIO(f.read())

            self.document.add_picture(image_stream, width=Inches(6))

        except subprocess.CalledProcessError as e:
            error_msg = (
                f"D2 CLI failed: {e.stderr}"
                if e.stderr
                else f"D2 CLI failed with code {e.returncode}"
            )

            self.document.add_paragraph(
                f"❌ Failed to render D2 diagram: {error_msg}", style="BodyText"
            )

            self.document.add_paragraph("Diagram syntax:", style="BodyText")

            self.document.add_paragraph(d2_code, style="BodyText")

        except FileNotFoundError:
            error_msg = "D2 CLI not found. Please install D2 first. Install guide: https://d2lang.com/tour/install"

            self.document.add_paragraph(
                f"❌ Failed to render D2 diagram: {error_msg}", style="BodyText"
            )

            self.document.add_paragraph("Diagram syntax:", style="BodyText")

            self.document.add_paragraph(d2_code, style="BodyText")

        except Exception as e:
            self.document.add_paragraph(
                f"❌ Failed to render D2 diagram: {str(e)}", style="BodyText"
            )

            self.document.add_paragraph("Diagram syntax:", style="BodyText")

            self.document.add_paragraph(d2_code, style="BodyText")

        finally:
            # Cleanup temp files

            if os.path.exists(temp_d2_file):
                os.remove(temp_d2_file)

            if os.path.exists(output_file):
                os.remove(output_file)

    def add_sad_documentation_to_docx(self, doc: Dict, sections: List[Dict]) -> None:
        """Add SAD documentation and its sections to the document with D2 support."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        self.document.add_page_break()
        self.add_styled_heading(
            doc.get("Title", "Software Architecture Document"), level=1
        )
        
        # Add document metadata
        metadata = [
            ("Type", doc.get("DocumentationType", "SAD")),
            ("Version", doc.get("Version", "N/A")),
            ("Description", doc.get("Description", ""))
        ]
        self._add_metadata_table(metadata)

        # Process main document content if exists
        if doc.get("Content"):
            self.add_styled_heading("Full Content", level=2)
            self._process_markdown_content(doc.get("Content", ""))

        # Add each section with enhanced processing
        for section in sorted(sections, key=lambda x: x.get("Order", 0)):
            self.document.add_page_break()
            self.add_styled_heading(section.get("Title", "Section"), level=2)
            
            # Section metadata
            section_metadata = []
            if section.get("SectionType"):
                section_metadata.append(("Section Type", section["SectionType"]))
            if section.get("Version"):
                section_metadata.append(("Version", section["Version"]))
            if section.get("Description"):
                section_metadata.append(("Description", section["Description"]))
            
            if section_metadata:
                self._add_metadata_table(section_metadata)

            # Process section content with both Mermaid and D2 support
            content = section.get("Content", "")
            if content:
                self.add_styled_heading("Section Content", level=3)
                self._process_markdown_content(content)  # This now handles both diagram types


    def _add_metadata_table(self, metadata: List[tuple]) -> None:
        """Add a formatted metadata table."""
        if not metadata:
            return
            
        table = self.document.add_table(rows=len(metadata), cols=2)
        table.style = 'Light Grid Accent 1'
        
        for i, (key, value) in enumerate(metadata):
            table.cell(i, 0).text = str(key)
            table.cell(i, 1).text = str(value)
            # Make key column bold
            table.cell(i, 0).paragraphs[0].runs[0].bold = True

    def _process_markdown_content(self, content: str) -> None:
        """Process markdown content maintaining structure and formatting."""
        if not content.strip():
            return
            
        # Extract and render D2 diagrams first (same pattern as Mermaid)
        d2_code_blocks = self.extract_d2_code(content)
        for d2_code in d2_code_blocks:
            self.add_d2_diagram(d2_code)  # Uses your existing method
        
        # Extract and render Mermaid diagrams
        mermaid_code_blocks = self.extract_mermaid_code(content)
        for mermaid_code in mermaid_code_blocks:
            self.add_mermaid_diagram(mermaid_code)
        
        # Remove both diagram types from content before processing text
        content_without_diagrams = self.remove_mermaid_code(content)
        content_without_diagrams = self.remove_d2_code(content_without_diagrams)
        
        # Split content into logical blocks
        blocks = self._split_content_into_blocks(content_without_diagrams)
        
        for block in blocks:
            if not block.strip():
                continue
                
            block_type, block_content = self._identify_block_type(block)
            
            if block_type == "heading":
                self._handle_heading(block_content)
            elif block_type == "bullet_list":
                self._handle_bullet_list(block_content)
            elif block_type == "table":
                self._handle_table(block_content)
            elif block_type == "code_block":
                self._handle_code_block(block_content)
            else:
                self._handle_paragraph(block_content)

    def _split_content_into_blocks(self, content: str) -> List[str]:
        """Split content into logical blocks for processing."""
        # Normalize line endings and clean up extra whitespace
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        content = re.sub(r'\n\s*\n', '\n\n', content.strip())
        
        # Split by double newlines but preserve mermaid blocks
        blocks = []
        current_block = []
        lines = content.split('\n')
        
        in_mermaid = False
        in_code_block = False
        
        for line in lines:
            if line.strip().startswith('```mermaid') or line.strip() == '<mermaid>':
                if current_block:
                    blocks.append('\n'.join(current_block))
                    current_block = []
                current_block = [line]
                in_mermaid = True
            elif line.strip() == '```' and in_mermaid:
                current_block.append(line)
                blocks.append('\n'.join(current_block))
                current_block = []
                in_mermaid = False
            elif line.strip() == '</mermaid>' and in_mermaid:
                current_block.append(line)
                blocks.append('\n'.join(current_block))
                current_block = []
                in_mermaid = False
            elif in_mermaid:
                current_block.append(line)
            elif line.strip() == '' and not current_block:
                continue
            elif line.strip() == '':
                if current_block:
                    blocks.append('\n'.join(current_block))
                    current_block = []
            else:
                current_block.append(line)
        
        if current_block:
            blocks.append('\n'.join(current_block))
        
        return [block.strip() for block in blocks if block.strip()]

    def _identify_block_type(self, block: str) -> tuple:
        """Identify the type of content block."""
        block = block.strip()
        
        # Mermaid diagram
        if (block.startswith('<mermaid>') or 
            block.startswith('```mermaid') or 
            '```mermaid' in block):
            return "mermaid", block
        
        # Heading (# ## ### etc.)
        if re.match(r'^#{1,6}\s+', block):
            return "heading", block
        
        # Bullet list - check if multiple lines start with bullets
        lines = block.split('\n')
        bullet_lines = [line for line in lines if re.match(r'^\s*[•\-\*]\s+', line)]
        if len(bullet_lines) >= 1:  # Even single bullet items should be treated as lists
            return "bullet_list", block
        
        # Table - check for pipe characters in a structured way
        if '|' in block:
            lines = [line.strip() for line in block.split('\n') if line.strip()]
            pipe_lines = [line for line in lines if '|' in line and len(line.split('|')) > 2]
            if len(pipe_lines) >= 2:  # At least header and one data row
                return "table", block
        
        # Code block
        if block.startswith('```') and block.endswith('```'):
            return "code_block", block
        
        return "paragraph", block

    def _handle_mermaid_diagram(self, block: str) -> None:
        """Extract and process Mermaid diagrams."""
        mermaid_code = self._extract_mermaid_code_from_block(block)
        if mermaid_code:
            self.add_mermaid_diagram(mermaid_code.strip())

    def _extract_mermaid_code_from_block(self, block: str) -> str:
        """Extract mermaid code from various block formats."""
        # Handle <mermaid> tags
        mermaid_match = re.search(r'<mermaid>(.*?)</mermaid>', block, re.DOTALL)
        if mermaid_match:
            return mermaid_match.group(1)
        
        # Handle ```mermaid code blocks
        mermaid_match = re.search(r'```mermaid\s*(.*?)\s*```', block, re.DOTALL)
        if mermaid_match:
            return mermaid_match.group(1)
        
        return block  # Return original if no specific pattern found

    def _handle_heading(self, block: str) -> None:
        """Handle markdown headings."""
        match = re.match(r'^(#{1,6})\s+(.+)', block.strip())
        if match:
            level = len(match.group(1))
            text = match.group(2).strip()
            # Limit heading levels to what DOCX supports
            self.add_styled_heading(text, level=min(level + 1, 6))  # +1 because main doc is level 1

    def _handle_bullet_list(self, block: str) -> None:
        """Handle bullet lists with proper formatting."""
        lines = block.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Handle different bullet formats
            bullet_match = re.match(r'^[•\-\*]\s+(.+)', line)
            if bullet_match:
                text = bullet_match.group(1)
                self.document.add_paragraph(text, style="List Bullet")
            else:
                # Handle continuation lines (indented text)
                if line.startswith('  ') or line.startswith('\t'):
                    self.document.add_paragraph(line.strip(), style="List Bullet")

    def _handle_table(self, block: str) -> None:
        """Handle markdown tables."""
        lines = [line.strip() for line in block.split('\n') if line.strip()]
        
        if len(lines) < 2:
            return
        
        # Parse table structure
        table_data = []
        headers = []
        
        for i, line in enumerate(lines):
            # Skip separator lines (like |---|---|)
            if re.match(r'^[\|\-\s:]+$', line):
                continue
                
            # Split by pipe and clean up
            cells = [cell.strip() for cell in line.split('|')]
            cells = [cell for cell in cells if cell]  # Remove empty cells from start/end
            
            if not cells:
                continue
                
            if not headers:
                headers = cells
            else:
                table_data.append(cells)
        
        if headers and table_data:
            self._add_formatted_table(headers, table_data)

    def _handle_code_block(self, block: str) -> None:
        """Handle code blocks."""
        # Extract code content
        if block.startswith('```') and block.endswith('```'):
            lines = block.split('\n')
            # Remove first and last lines (```)
            code_content = '\n'.join(lines[1:-1])
        else:
            code_content = block
        
        # Add as monospace paragraph
        paragraph = self.document.add_paragraph()
        run = paragraph.add_run(code_content)
        run.font.name = 'Courier New'
        run.font.size = docx.shared.Pt(9)
        paragraph.paragraph_format.left_indent = docx.shared.Inches(0.5)

    def _handle_paragraph(self, block: str) -> None:
        """Handle regular paragraphs with inline formatting."""
        if not block.strip():
            return
            
        paragraph = self.document.add_paragraph(style="BodyText")
        self._add_formatted_text_to_paragraph(paragraph, block)

    def _add_formatted_table(self, headers: List[str], rows: List[List[str]]) -> None:
        """Add a formatted table to the document."""
        max_cols = max(len(headers), max(len(row) for row in rows) if rows else 0)
        
        table = self.document.add_table(rows=len(rows) + 1, cols=max_cols)
        table.style = 'Light Grid Accent 1'
        
        # Add headers
        for i, header in enumerate(headers):
            if i < max_cols:
                cell = table.cell(0, i)
                cell.text = header
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.bold = True
        
        # Add data rows
        for row_idx, row in enumerate(rows):
            for col_idx, cell_data in enumerate(row):
                if col_idx < max_cols:
                    table.cell(row_idx + 1, col_idx).text = str(cell_data)

    def _add_formatted_text_to_paragraph(self, paragraph, text: str) -> None:
        """Add text with inline formatting (bold, etc.) to a paragraph."""
        # Handle bold text **text**
        parts = re.split(r'(\*\*.*?\*\*)', text)
        
        for part in parts:
            if re.match(r'\*\*.*\*\*', part):
                # Bold text
                clean_text = part.strip('*')
                run = paragraph.add_run(clean_text)
                run.bold = True
            else:
                paragraph.add_run(part)

    # Improved Mermaid extraction methods to replace your existing ones
    def extract_mermaid_code(self, content: str) -> List[str]:
        """Extract all Mermaid code blocks from the content with better pattern matching."""
        patterns = [
            r'<mermaid>(.*?)</mermaid>',
            r'```mermaid\s*(.*?)\s*```'
        ]
        
        mermaid_blocks = []
        for pattern in patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            mermaid_blocks.extend([match.strip() for match in matches])
        
        return mermaid_blocks

    def remove_mermaid_code(self, content: str) -> str:
        """Remove all Mermaid code blocks from the content more reliably."""
        patterns = [
            r'<mermaid>.*?</mermaid>',
            r'```mermaid\s*.*?\s*```'
        ]
        
        cleaned_content = content
        for pattern in patterns:
            cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.DOTALL)
        
        # Clean up extra whitespace
        cleaned_content = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_content)
        return cleaned_content.strip()
    def extract_d2_code(self, content: str) -> List[str]:
        """Extract all D2 code blocks from the content (similar to mermaid extraction)."""
        d2_patterns = [
            r'```d2(.*?)```',           # ```d2 code blocks
            r'"d2\s*(.*?)(?=\n\n|\Z)',  # quoted D2 content
        ]
        
        d2_code_blocks = []
        for pattern in d2_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            d2_code_blocks.extend([match.strip() for match in matches if match.strip()])
        
        return d2_code_blocks

    def remove_d2_code(self, content: str) -> str:
        """Remove all D2 code blocks from the content (similar to mermaid removal)."""
        d2_patterns = [
            r'```d2(.*?)```',
            r'"d2\s*(.*?)(?=\n\n|\Z)',
        ]
        
        content_without_d2 = content
        for pattern in d2_patterns:
            content_without_d2 = re.sub(pattern, "", content_without_d2, flags=re.DOTALL)
        
        # Clean up extra whitespace
        content_without_d2 = re.sub(r'\n\s*\n\s*\n', '\n\n', content_without_d2)
        return content_without_d2.strip()
