{"API": {"title_prefix": "API Documentation", "root_description_template": "Complete API documentation for {title} ({type}). {description}", "sections": [{"title": "Resource Description", "type": "resource_description", "description": "Detailed description of the API resource and its capabilities", "order": 1}, {"title": "Endpoints and Methods", "type": "endpoints_methods", "description": "List of all available endpoints and their HTTP methods", "order": 2}, {"title": "Parameters", "type": "parameters", "description": "Detailed description of all request parameters", "order": 3}, {"title": "Request Examples", "type": "request_examples", "description": "Example requests for each endpoint", "order": 4}, {"title": "Response Schemas", "type": "response_schemas", "description": "Response formats and examples", "order": 5}]}, "PRD": {"title_prefix": "Product Requirements Document", "root_description_template": "Product Requirements Documentation for {title}. {description}", "sections": [{"title": "Product Overview", "type": "product_overview", "description": "providing a high-level overview of the product, its core value proposition, and key objectives. This section should be informative enough for stakeholders to quickly understand the product's purpose.Start with a one-paragraph product overview. Do not include timelines and Team compositions. Here is a guide for constructing sub section: - Define the primary purpose and value proposition - List key differentiators from existing solutions - Specify the target market segment", "order": 1}, {"title": "Problem Statement", "type": "problem_statement", "description": "clearly articulating the specific problems or needs the product aims to address. This includes market analysis, user pain points, and any relevant research or data supporting the problem's existence and significance. Here is a guide for constructing sub section: 1. Describe the current situation and challenges 2. Provide market research data supporting the problem's existence 3. Outline the impact of not solving this problem 4. Include relevant user feedback or market insights 5. Quantify the problem's scope. Key areas to Cover with above content: - What specific problems are users facing? - Why do existing solutions fall short? - What evidence supports this being a significant problem?", "order": 2}, {"title": "Product Goals and Objectives", "type": "product_goals", "description": "outlining specific, measurable targets the product should achieve. This includes business goals, user-centric objectives, and success metrics that will determine if the product meets its intended purpose. Use Markdown tables where its needed. Here is a guide for constructing sub section: 1. Define SMART goals (Specific, Measurable, Achievable, Relevant, Time-bound) 2. List primary objectives (3-5 maximum) 3. Define success metrics for each goal 4. Align objectives with company strategy", "order": 3}, {"title": "User Personas and Target Audience", "type": "user_personas", "description": "intended users in detail. This section includes demographic information, behavioral patterns, needs, goals, and frustrations of your target users. Include use cases and user journeys to illustrate how different personas will interact with the product.  Use Markdown tables where its needed. Here is a guide for constructing sub section: 1. Create detailed user profiles including: - Demographics - Behavioral patterns - Goals and motivations - Pain points and frustrations 2. Develop user scenarios 3. Map user journeys 4. Define use cases", "order": 4}, {"title": "Functional Requirements", "type": "functional_requirements", "description": "Functional Requirements details the specific features and capabilities the product must have. This section should describe what the product does, how it behaves, and what functionalities it offers. Each requirement should be specific, measurable, and testable. Here is a guide for constructing sub section: 1. List features in priority order 2. For each feature, specify: - Detailed description - User benefit - Acceptance criteria - Dependencies - Priority level 3. Include input and output requirements 4. Define system behaviors and interactions 5. Specify error handling scenarios", "order": 5}, {"title": "Non-Functional Requirements", "type": "non_functional_requirements", "description": "Non-Functional Requirements covers aspects like performance specifications, security requirements, scalability needs, and other technical constraints. This includes loading times, uptime requirements, compliance needs, and any other quality attributes. Here is a guide for constructing sub section: 1. Define performance requirements: - Load times - Response times - Concurrent users 2. Specify security requirements 3. List compliance needs 4. Define scalability requirements 5. Include reliability metrics 6. Specify compatibility requirements", "order": 6}, {"title": "Success Metrics", "type": "success_metrics", "description": "Success Criteria and Metrics defines how success will be measured, including specific KPIs, performance metrics, and acceptance criteria for each requirement.  Use Markdown tables where its needed. Here is a guide for constructing sub section: 1. Define key performance indicators (KPIs) 2. Set baseline metrics 3. Establish target metrics 4. Define measurement methods 5. Set up tracking mechanisms 6. Create reporting structure", "order": 7}]}, "SAD": {"title_prefix": "Software Architecture Document", "root_description_template": "Software Architecture Documentation for {title}. {description}", "sections": [{"title": "Architecture Overview", "type": "architecture_overview", "description": "Provides a comprehensive high-level overview of the system architecture. This section should include the architectural style/pattern chosen, key architectural principles, system-wide design decisions, and technical constraints. Include diagrams appropriately using mermaid with correct syntax , wrap the diagrams <mermaid> tags. Here is a guide for constructing sub section: 1. Define the overall architectural style (e.g., microservices, monolithic, event-driven) 2. List key architectural principles and patterns being followed 3. Outline major system components and their relationships 4. Generate a Architecture Overview Diagram 5. Describe technical constraints and assumptions", "order": 1}, {"title": "System Context", "type": "system_context", "description": "Details the system boundaries, external dependencies, and interactions with other systems. This section should clearly define what is within and outside the system scope.\n\nHere is a guide for constructing sub section:\n1. Identify all external systems and interfaces\n2. Define system boundaries and scope\n3. Document all integration points\n4. Describe communication protocols and methods\n5. Include context diagrams showing system interactions (all Mermaid diagrams must be wrapped in <mermaid>...</mermaid> tags)\n6. d2 syntax:\nGenerate a Generic Cloud Infrastructure Diagram using D2 syntax.:\n\n   - Use **D2 syntax**.\n   - Ensure the output is valid and renderable D2 code (no syntax errors).\n   - Illustrate how the system and its containers are deployed within a virtual private cloud (VPC) or equivalent.\n   - Show key infrastructure components such as load balancers, compute clusters (VMs or container orchestrators), managed databases, object/block storage, and monitoring/logging services.\n   - Indicate the flow of traffic from external users through the load balancer to the application containers, and from containers to databases and storage.\n   - Use generic names (e.g., \"Compute Cluster\", \"Managed Database\") to ensure cloud-agnostic representation.\n   - Follow D2 validation rules.\n\n   Generic Infrastructure Diagram Reference (D2 syntax):\n   <d2>\n   title: \"Generic Cloud Infrastructure Architecture\"\n\n      Internet: \"🌍 Internet / External Users\" {\n      shape: cloud\n      style.fill: \"#e6f7ff\"\n      }\n\n      System: {\n      style: {\n         stroke-dash: 4\n         fill: \"#f2f2f2\"\n      }\n\n      LB: \"🔀 Load Balancer\" {\n         shape: diamond\n         style.fill: \"#d9ead3\"\n      }\n\n      ComputeCluster: \"⚙️ Compute Cluster (VMs / Containers)\" {\n         shape: rectangle\n         style.fill: \"#f9cb9c\"\n      }\n\n      App1: \"📦 Application Container 1\" {\n         shape: rectangle\n         style.fill: \"#ffe6cc\"\n      }\n\n      App2: \"📦 Application Container 2\" {\n         shape: rectangle\n         style.fill: \"#ffe6cc\"\n      }\n\n      DB: \"🗄️ Managed Database\" {\n         shape: cylinder\n         style.fill: \"#d9d2e9\"\n      }\n\n      Storage: \"💾 Object / Block Storage\" {\n         shape: cylinder\n         style.fill: \"#cce5ff\"\n      }\n\n      Monitoring: \"📊 Monitoring & Logging\" {\n         shape: rectangle\n         style.fill: \"#c9daf8\"\n      }\n\n      # Connections inside System\n      LB -> ComputeCluster: \"Distribute Traffic\" {\n         style.stroke: \"#4caf50\"\n         style.stroke-width: 2\n      }\n\n      ComputeCluster -> App1: \"Internal Traffic\"\n      ComputeCluster -> App2: \"Internal Traffic\"\n\n      App1 -> DB: \"SQL Queries\"\n      App2 -> DB: \"SQL Queries\"\n      App1 -> Storage: \"File/Data Access\"\n      App2 -> Storage: \"File/Data Access\"\n\n      App1 -> Monitoring: \"Logs & Metrics\"\n      App2 -> Monitoring: \"Logs & Metrics\"\n      DB -> Monitoring: \"Performance Metrics\"\n      }\n\n      # External connection\n      Internet -> System.LB: \"HTTPS Requests\" {\n      style.stroke: \"#2196f3\"\n      style.stroke-width: 2\n   }\n   </d2>\n\n   Note: When generating the diagram:\n   - Replace generic names with actual infrastructure component names if available.\n   - Update descriptions to match the actual deployment context.\n   - Show all relevant infrastructure components and their relationships.\n   - Ensure the diagram remains cloud-agnostic unless a specific provider is required.\n\n(all D2 diagrams must be wrapped in <d2>...</d2> tags)\n\n7. Generate Integration Architecture Diagram:\n\n   - Use **D2** syntax.\n   - Ensure the output is valid and renderable D2 code (no syntax errors).\n   - Illustrate how the system integrates with external systems (e.g., CRM, ERP, Payment Gateway, Third-Party APIs).\n   - Show interfaces and integration patterns (e.g., REST, SOAP, message queues, events).\n   - Highlight data flow and communication between external systems, the integration/API layer, core services, and internal data stores.\n   - Include monitoring/logging where relevant.\n   - Use generic, cloud-agnostic names unless specific technologies are provided.\n   - Follow D2 validation rules.\n\n   Generic Integration Architecture Diagram Reference (D2 syntax):\n   <d2>\n         title: \"Generic Integration Architecture - Data Flow & Interfaces\"\n\n         CRM: \"💼 CRM System\" {\n         shape: cylinder\n         style.fill: \"#cce5ff\"\n         }\n         ERP: \"🏢 ERP System\" {\n         shape: cylinder\n         style.fill: \"#cce5ff\"\n         }\n         Payment: \"💳 Payment Gateway\" {\n         shape: rectangle\n         style.fill: \"#ffe6cc\"\n         }\n         ThirdParty: \"☁️ Third-Party API\" {\n         shape: cloud\n         style.fill: \"#e6f7ff\"\n         }\n         Messaging: \"📩 Message Queue / Event Bus\" {\n         shape: parallelogram\n         style.fill: \"#fff2cc\"\n         }\n         \n         System: {\n         shape: cloud\n         style: {\n            stroke-dash: 4\n            fill: \"#f2f2f2\"\n         }\n         \n         API: \"🌐 Integration Layer / API Gateway\" {\n            shape: rectangle\n            style.fill: \"#d9ead3\"\n         }\n         Service1: \"⚙️ Core Service A\" {\n            shape: rectangle\n            style.fill: \"#f9cb9c\"\n         }\n         Service2: \"⚙️ Core Service B\" {\n            shape: rectangle\n            style.fill: \"#f9cb9c\"\n         }\n         DB: \"🗄️ Application Database\" {\n            shape: cylinder\n            style.fill: \"#d9d2e9\"\n         }\n         Monitoring: \"📊 Monitoring & Logging\" {\n            shape: rectangle\n            style.fill: \"#c9daf8\"\n         }\n         \n         API -> Service1\n         API -> Service2\n         Service1 -> DB\n         Service2 -> DB\n         Service1 -> Monitoring\n         Service2 -> Monitoring\n         DB -> Monitoring\n         }\n\n         CRM -> System.API: \"🌐 REST\" {\n         style.stroke: \"#4caf50\"\n         style.stroke-width: 2\n         }\n         ERP -> System.API: \"📜 SOAP\" {\n         style.stroke: \"#ff9800\"\n         style.stroke-dash: 4\n         style.stroke-width: 2\n         }\n         Payment -> System.API: \"💳 REST\" {\n         style.stroke: \"#4caf50\"\n         style.stroke-width: 2\n         }\n         System.API -> Messaging: \"📩 Publish Events\" {\n         style.stroke: \"#2196f3\"\n         style.stroke-dash: 3\n         }\n         Messaging -> System.Service1: \"📥 Subscribe Events\" {\n         style.stroke: \"#2196f3\"\n         style.stroke-dash: 3\n         }\n         System.Service2 -> ThirdParty: \"🔐 REST / OAuth2\" {\n         style.stroke: \"#9c27b0\"\n         style.stroke-width: 2\n         }\n   </d2>\n\n   Note: When generating the diagram:\n   - Replace CRM, ERP, Payment, ThirdParty with actual external system names if available.\n   - Replace REST, SOAP, Events with the actual integration patterns used.\n   - Extend with additional services/interfaces depending on the real integration context.\n   - Ensure data flow arrows clearly indicate direction of communication.\n\n8. 8. Generate High-Level Data Model (Data Architecture) Diagram:\n\n- Use D2 syntax.\n- Ensure the output is valid and renderable D2 code (no syntax errors).\n- Represent a high-level ER diagram or class diagram showing entities, attributes, and relationships.\n- Include data sources, sinks, and flows where applicable (e.g., external systems, APIs, databases, warehouses).\n- Show data governance aspects such as ownership (e.g., “owned by HR”, “managed by IT”).\n- Use generic, technology-agnostic labels unless specific technologies are provided.\n- Ensure relationships are directional and cardinalities are expressed (1:1, 1:N, M:N).\n- Follow D2 validation rules.\n\nIMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:\n- The identified users from the requirements\n- The actual relationships and interfaces discovered\n- The specific system name and purpose from the project context\n\nGeneric High-Level Data Model (Data Architecture) Reference:\n```d2\n   title: \"High-Level ER Data Model - Entities, Attributes, and Relationships\"\n\n   User: {\n   shape: sql_table\n   id: \"int (PK)\"\n   name: \"string\"\n   role: \"string\"\n   email: \"string\"\n   department_id: \"int (FK)\"\n   governance: \"HR_Ownership_UserMgmt\"\n   }\n   \n   WorkPermit: {\n   shape: sql_table\n   id: \"int (PK)\"\n   status: \"string\"\n   start_date: \"date\"\n   end_date: \"date\"\n   applicant_id: \"int (FK)\"\n   department_id: \"int (FK)\"\n   governance: \"Compliance_WorkPermit_Control\"\n   }\n   \n   Department: {\n   shape: sql_table\n   id: \"int (PK)\"\n   name: \"string\"\n   manager_id: \"int (FK)\"\n   governance: \"HR_Ownership_DepartmentMgmt\"\n   }\n   \n   Project: {\n   shape: sql_table\n   id: \"int (PK)\"\n   name: \"string\"\n   description: \"string\"\n   department_id: \"int (FK)\"\n   governance: \"PMO_ProjectOversight\"\n   }\n   \n   Task: {\n   shape: sql_table\n   id: \"int (PK)\"\n   title: \"string\"\n   due_date: \"date\"\n   assigned_to: \"int (FK)\"\n   project_id: \"int (FK)\"\n   governance: \"PMO_TaskTracking\"\n   }\n   \n   Asset: {\n   shape: sql_table\n   id: \"int (PK)\"\n   name: \"string\"\n   type: \"string\"\n   department_id: \"int (FK)\"\n   governance: \"IT_AssetLifecycleMgmt\"\n   }\n   \n   Training: {\n   shape: sql_table\n   id: \"int (PK)\"\n   title: \"string\"\n   date: \"date\"\n   instructor: \"string\"\n   governance: \"Learning_TrainingPrograms\"\n   }\n   \n   ComplianceReport: {\n   shape: sql_table\n   id: \"int (PK)\"\n   work_permit_id: \"int (FK)\"\n   report_date: \"date\"\n   status: \"string\"\n   governance: \"Compliance_AuditTrailMgmt\"\n   }\n   \n   Incident: {\n   shape: sql_table\n   id: \"int (PK)\"\n   description: \"string\"\n   reported_by: \"int (FK)\"\n   related_asset: \"int (FK)\"\n   severity: \"string\"\n   governance: \"Safety_IncidentControl\"\n   }\n   \n   User -> WorkPermit: \"applies (1:N)\" {\n   style.stroke: \"#1890FF\"\n   }\n   User -> Department: \"belongs to (N:1)\" {\n   style.stroke: \"#52C41A\"\n   }\n   Department -> WorkPermit: \"approves (1:N)\" {\n   style.stroke: \"#FA8C16\"\n   }\n   Department -> Project: \"owns (1:N)\" {\n   style.stroke: \"#722ED1\"\n   }\n   Project -> Task: \"contains (1:N)\" {\n   style.stroke: \"#722ED1\"\n   }\n   User -> Task: \"assigned (1:N)\" {\n   style.stroke: \"#1890FF\"\n   }\n   Department -> Asset: \"manages (1:N)\" {\n   style.stroke: \"#13C2C2\"\n   }\n   Asset -> Incident: \"involved in (1:N)\" {\n   style.stroke: \"#D46B08\"\n   }\n   User -> Incident: \"reports (1:N)\" {\n   style.stroke: \"#D46B08\"\n   }\n   WorkPermit -> ComplianceReport: \"documented by (1:1)\" {\n   style.stroke: \"#CF1322\"\n   }\n   User -> Training: \"attends (N:M)\" {\n   style.stroke: \"#2F54EB\"\n   }\n   \n   HR_System: {\n   shape: cloud\n   label: \"HR System | Employee Master Data\"\n   style.fill: \"#F0F5FF\"\n   style.stroke: \"#2F54EB\"\n   }\n   \n   Gov_API: {\n   shape: cloud\n   label: \"Government API | Permit Verification\"\n   style.fill: \"#FFF1F0\"\n   style.stroke: \"#CF1322\"\n   }\n   \n   Analytics: {\n   shape: cylinder\n   label: \"Analytics | Data Warehouse\"\n   style.fill: \"#F9F0FF\"\n   style.stroke: \"#722ED1\"\n   }\n   \n   DataLake: {\n   shape: cylinder\n   label: \"Data Lake | Historical Storage\"\n   style.fill: \"#E6FFFB\"\n   style.stroke: \"#13C2C2\"\n   }\n   \n   AuditLogs: {\n   shape: cylinder\n   label: \"Audit Logs | Immutable Storage\"\n   style.fill: \"#FFFBE6\"\n   style.stroke: \"#D4B106\"\n   }\n   \n   HR_System -> User: \"Sync Employee Data\" {\n   style.stroke: \"#2F54EB\"\n   }\n   User -> WorkPermit: \"Submit Application\" {\n   style.stroke: \"#1890FF\"\n   }\n   WorkPermit -> Gov_API: \"Validation Request\" {\n   style.stroke: \"#CF1322\"\n   }\n   Gov_API -> WorkPermit: \"Validation Response\" {\n   style.stroke: \"#CF1322\"\n   }\n   Department -> WorkPermit: \"Approval Workflow\" {\n   style.stroke: \"#FA8C16\"\n   }\n   WorkPermit -> Analytics: \"Permit Metrics\" {\n   style.stroke: \"#722ED1\"\n   }\n   User -> Analytics: \"User Activity\" {\n   style.stroke: \"#722ED1\"\n   }\n   WorkPermit -> DataLake: \"Archive\" {\n   style.stroke: \"#13C2C2\"\n   }\n   User -> AuditLogs: \"Audit Trail\" {\n   style.stroke: \"#D4B106\"\n   }\n   Incident -> Analytics: \"Safety Metrics\" {\n   style.stroke: \"#722ED1\"\n   }\n   Training -> Analytics: \"Training Effectiveness\" {\n   style.stroke: \"#722ED1\"\n   }\n   # Data Flows\n   HR_System -> User: \"Sync Employee Data\"\n   User -> WorkPermit: \"Application Data\"\n   WorkPermit -> Gov_API: \"Validation Request\"\n   WorkPermit -> Analytics: \"Permit Metrics\"\n   User -> Analytics: \"User Activity Data\"\n   Department -> Analytics: \"Org Structure Data\"\n``` \n\nNotes for Generation:\n- Replace User, WorkPermit, etc., with actual domain entities.\n- Extend with additional entities (e.g., Department, Project, Document) as per context.\n- Add data governance tags (owner, steward, regulator).\n- Show flows to/from external systems (data sources/sinks).\n- Keep diagram readable and at high level (wrap in <d2>...</d2> tags)\n9. List all external dependencies and third-party services", "order": 2}, {"title": "Design Decisions", "type": "design_decisions", "description": "Documents key architectural decisions and their rationale, including alternatives considered and trade-offs made. Here is a guide for constructing sub section: 1. List significant architectural decisions using ADR (Architecture Decision Record) format 2. For each decision include: - Context and problem statement - Considered alternatives - Chosen solution and justification - Consequences and trade-offs 3. Document technical constraints that influenced decisions 4. Include relevant proof-of-concepts or prototypes 5. Reference any architectural fitness functions", "order": 3}, {"title": "Component Design", "type": "component_design", "description": "Provides detailed design of system components, their responsibilities, and interactions. This section should cover both high-level components and their internal structures. Include diagrams appropriately using mermaid with correct syntax , wrap the diagrams with <mermaid> tags. Here is a guide for constructing sub section: 1. Define component boundaries and responsibilities 2. Document component interfaces and APIs 3. Describe internal component structure and design patterns 4. Detail component interactions and dependencies 5. Generate sequence diagram valid mermaid syntax 6. Specify error handling and fault tolerance mechanisms 7. Document component configuration and deployment requirements", "order": 4}, {"title": "Data Architecture", "type": "data_architecture", "description": "Comprehensive coverage of data architecture including data models, storage solutions, and data flow patterns. Use Markdown tables where its needed. Here is a guide for constructing sub section: 1. Define data models and schemas 2. Document database design and storage solutions 3. Describe data flow patterns and ETL processes 4. Specify data retention and archival strategies 5. Include data security and privacy considerations 6. Detail backup and recovery procedures 7. Document data consistency and transaction management approaches 8. Specify caching strategies and data access patterns 9. Generate a High-Level Data Model. It must be either ER diagram or Class Diagram.", "order": 5}, {"title": "Security Architecture", "type": "security_architecture", "description": "Details the security design, including authentication, authorization, data protection, and security controls. Here is a guide for constructing sub section: 1. Define authentication and authorization mechanisms 2. Document security controls and compliance requirements 3. Describe data encryption and protection measures 4. Specify security monitoring and logging approaches 5. Detail identity management and access control 6. Include security testing and vulnerability assessment procedures 7. Document incident response and security operations procedures 8. Specify network security and firewall configurations", "order": 6}, {"title": "Performance Considerations", "type": "performance", "description": "Addresses performance design, scalability strategies, and capacity planning. Here is a guide for constructing sub section: 1. Define performance requirements and SLAs 2. Document scalability strategies (vertical/horizontal) 3. Specify capacity planning approach and metrics 4. Detail caching strategies and content delivery 5. Include load balancing and failover mechanisms 6. Document performance monitoring and optimization approaches 7. Specify resource utilization targets and limits 8. Include performance testing strategies and benchmarks", "order": 7}, {"title": "Deployment and Infrastructure Architecture", "type": "deployment_infrastructure_architecture", "description": "Comprehensive coverage of deployment and infrastructure architecture including topology, environments, scalability, and network overview. Here is a guide for constructing sub sections: 1. Provide a deployment topology diagram (cloud/on-prem, network zones) 2. Define environments (dev, test, staging, prod) 3. Describe scalability, availability, and disaster recovery strategy 4. Document network and connectivity overview 5. Explain how the system will be deployed, scaled, and maintained 6. Include an example diagram titled 'Deployment Topology' to illustrate the architecture. ", "order": 8}, {"title": "Non-Functional Requirements", "type": "non_functional_requirements", "description": "Document the system's quality attributes and constraints including performance, scalability, reliability, maintainability, usability, and compliance.", "order": 9}, {"title": "Assumptions and Constraints", "type": "assumptions_constraints", "description": "Clarify key assumptions made during design and constraints imposed by technology, policy, or environment to define design boundaries and limitations.", "order": 10}, {"title": "Risks and Mitigation Strategies", "type": "risks_mitigation", "description": "Identify potential architectural risks and provide mitigation or contingency plans to proactively address issues that could impact delivery or operation.", "order": 11}, {"title": "Glossary", "type": "glossary", "description": "Define key terms, acronyms, and abbreviations to ensure clarity and a common understanding among all readers.", "order": 12}, {"title": "Appendices", "type": "appendices", "description": "Include supporting documents, reference links, and change logs to provide additional context and traceability.", "order": 13}, {"title": "Conclusion", "type": "conclusion", "description": "Summarize the importance of a well-structured HLD document for aligning stakeholders, guiding design, and ensuring scalability, robustness, and maintainability. Highlight the role of diagrams in maximizing understanding and traceability.", "order": 14}]}, "COMPONENT_DOCUMENTATION": {"title_prefix": "Component Documentation", "root_description_template": "Technical documentation for {title} component. {description}", "sections": [{"title": "Component Overview", "type": "component_overview", "description": "Overview of the component's purpose and functionality", "order": 1}, {"title": "Technical Architecture", "type": "technical_architecture", "description": "Internal architecture and design patterns", "order": 2}, {"title": "Dependencies", "type": "dependencies", "description": "External dependencies and requirements", "order": 3}, {"title": "Integration Guide", "type": "integration_guide", "description": "Instructions for integrating with the component", "order": 4}, {"title": "Configuration", "type": "configuration", "description": "Configuration options and settings", "order": 5}, {"title": "Usage Examples", "type": "usage_examples", "description": "Code examples and usage scenarios", "order": 6}]}}